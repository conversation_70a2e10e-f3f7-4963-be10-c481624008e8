# MCP (Model Context Protocol) Connection Establishment Guide

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Connection Types](#connection-types)
3. [Remote HTTP/SSE Transport](#remote-httpsse-transport)
4. [Local Stdio Transport](#local-stdio-transport)
5. [Message Exchange Patterns](#message-exchange-patterns)
6. [Connection Lifecycle Management](#connection-lifecycle-management)
7. [Security Considerations](#security-considerations)
8. [Performance Characteristics](#performance-characteristics)

## Architecture Overview

The Alpine Intellect Desktop App implements a sophisticated MCP (Model Context Protocol) client-server architecture where the desktop application serves as the **MCP Client** and connects to various external services acting as **MCP Servers**.

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                Alpine Intellect Desktop App                 │
│                     (MCP Client)                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Service Registry │  │ OAuth Service   │  │ Config Mgr   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ HTTP/SSE        │  │ Stdio Transport │                   │
│  │ Transport       │  │                 │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
           │                           │
           ▼                           ▼
┌─────────────────┐         ┌─────────────────┐
│ Remote MCP      │         │ Local MCP       │
│ Servers         │         │ Server Process  │
│ (GitHub, etc.)  │         │ (Figma, etc.)   │
└─────────────────┘         └─────────────────┘
```

### Core Architectural Principles

1. **Transport Abstraction**: Both HTTP/SSE and stdio transports implement a common interface
2. **Service Isolation**: Each MCP server operates independently with its own authentication
3. **Unified Management**: Single service registry manages all connections regardless of transport
4. **Security First**: OAuth 2.0 with PKCE for authentication, secure token management

## Connection Types

The system supports two distinct transport mechanisms, each optimized for different deployment scenarios:

### Transport Selection Matrix

| Transport | Use Case | Latency | Security | Deployment |
|-----------|----------|---------|----------|------------|
| HTTP/SSE  | Remote services, cloud APIs | Network dependent | TLS + OAuth | External servers |
| Stdio     | Local tools, privacy-focused | Process IPC (~μs) | Process isolation | Local executables |

### Configuration-Driven Transport Selection

```typescript
// Configuration determines transport type
interface ServerConfiguration {
  transport: 'http-sse' | 'stdio' | 'websocket';
  
  // HTTP/SSE specific
  endpoints?: {
    http: string;
    sse: string;
    discovery?: string;
  };
  
  // Stdio specific
  stdio?: {
    executable: string;
    args?: string[];
    env?: Record<string, string>;
    restart_delay?: number;
    max_restarts?: number;
  };
}
```

## Remote HTTP/SSE Transport

### Connection Establishment Sequence

```mermaid
sequenceDiagram
    participant Client as Alpine Intellect Client
    participant OAuth as OAuth Service
    participant Discovery as Discovery Service
    participant Server as Remote MCP Server
    participant SSE as SSE Endpoint

    Note over Client,SSE: Phase 1: Authentication
    Client->>OAuth: 1. Check existing auth status
    alt No valid token
        OAuth->>Client: 2. Generate authorization URL (PKCE)
        Client->>OAuth: 3. User completes OAuth flow
        OAuth->>Client: 4. Exchange code for tokens
    end

    Note over Client,SSE: Phase 2: Service Discovery (Optional)
    Client->>Discovery: 5. Discover OAuth metadata
    Discovery->>Client: 6. Return server capabilities

    Note over Client,SSE: Phase 3: Transport Initialization
    Client->>Server: 7. HTTP health check
    Server->>Client: 8. Health status
    Client->>SSE: 9. Establish SSE connection
    SSE->>Client: 10. Connection established

    Note over Client,SSE: Phase 4: MCP Handshake
    Client->>Server: 11. MCP initialize request
    Server->>Client: 12. Server capabilities response
    Client->>Server: 13. Tools/resources discovery
    Server->>Client: 14. Available tools list

    Note over Client,SSE: Phase 5: Operational
    loop Tool Execution
        Client->>Server: HTTP POST /tools/call
        Server->>Client: Tool execution result
    end
    
    loop Real-time Events
        SSE->>Client: Server-sent events
    end
```

### Implementation Details

#### 1. OAuth Authentication Flow

```typescript
// src/electron/mcp/clients/remote-service-client.ts
async initialize(): Promise<void> {
  console.log(`Initializing ${this.serviceId} service...`);

  try {
    // Check authentication status
    const authStatus = await this.oauthService.getAuthStatus();
    if (!authStatus.authenticated) {
      console.log(`${this.serviceId} not authenticated, authentication required`);
      return;
    }

    // Initialize transport with authenticated context
    await this.transport.initialize();
    
    // Set up message handlers for real-time events
    this.setupMessageHandlers();
    
    // Perform service-specific initialization
    await this.performServiceSpecificInitialization();

    this.initialized = true;
    console.log(`✅ ${this.serviceId} service initialized successfully`);
  } catch (error) {
    console.error(`❌ Failed to initialize ${this.serviceId} service:`, error);
    throw error;
  }
}
```

#### 2. HTTP Transport Initialization

```typescript
// src/electron/mcp/transports/http-sse-transport.ts
async initialize(): Promise<void> {
  console.log(`Initializing HTTP/SSE transport for ${this.config.httpEndpoint}`);

  try {
    // Perform health check to verify server availability
    await this.testConnection();
    console.log('✅ Health check passed');

    // Initialize Server-Sent Events for real-time communication
    await this.initializeSSE();
    console.log('✅ SSE initialized');

    this.connectionStartTime = Date.now();
    this.initialized = true;
    console.log('✅ HTTP/SSE transport initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize HTTP/SSE transport:', error);
    throw error;
  }
}

private async testConnection(): Promise<void> {
  const accessToken = await this.config.getAccessToken();
  
  const response = await fetch(`${this.config.httpEndpoint}/health`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
  }
}
```

#### 3. Server-Sent Events Setup

```typescript
private async initializeSSE(): Promise<void> {
  const accessToken = await this.config.getAccessToken();
  
  // Establish SSE connection with authentication
  this.eventSource = new EventSource(
    `${this.config.sseEndpoint}?access_token=${accessToken}`
  );
  
  this.eventSource.onopen = () => {
    console.log('✅ SSE connection established');
    this.metrics.connectionUptime = Date.now();
  };
  
  this.eventSource.onmessage = (event) => {
    try {
      const message = JSON.parse(event.data) as MCPMessage;
      this.handleIncomingMessage(message);
    } catch (error) {
      console.error('Failed to parse SSE message:', error);
    }
  };
  
  this.eventSource.onerror = (error) => {
    console.error('SSE connection error:', error);
    this.handleConnectionError(error);
  };
}
```

#### 4. MCP Protocol Handshake

```typescript
async sendRequest(request: MCPRequest): Promise<MCPResponse> {
  const startTime = Date.now();
  this.metrics.requestCount++;

  try {
    const accessToken = await this.config.getAccessToken();
    
    const response = await fetch(this.config.httpEndpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const mcpResponse = await response.json() as MCPResponse;
    
    // Update performance metrics
    const responseTime = Date.now() - startTime;
    this.updateMetrics(responseTime);
    
    return mcpResponse;
  } catch (error) {
    this.metrics.errorCount++;
    console.error(`Request failed for ${request.method}:`, error);
    throw error;
  }
}
```

## Local Stdio Transport

### Connection Establishment Sequence

```mermaid
sequenceDiagram
    participant Client as Alpine Intellect Client
    participant OAuth as OAuth Service
    participant ProcessMgr as Process Manager
    participant MCPProcess as Local MCP Process
    participant Stdio as Stdin/Stdout Transport

    Note over Client,Stdio: Phase 1: Pre-flight Checks
    Client->>OAuth: 1. Verify authentication status
    OAuth->>Client: 2. Return access token (if available)
    
    Note over Client,Stdio: Phase 2: Environment Preparation
    Client->>ProcessMgr: 3. Prepare environment variables
    Note over ProcessMgr: Inject: ACCESS_TOKEN, CLIENT_ID, etc.
    
    Note over Client,Stdio: Phase 3: Process Spawning
    ProcessMgr->>MCPProcess: 4. spawn(executable, args, {env, stdio})
    MCPProcess->>ProcessMgr: 5. Process started (PID)
    ProcessMgr->>Client: 6. Process ready signal
    
    Note over Client,Stdio: Phase 4: Transport Initialization
    Client->>Stdio: 7. Initialize stdio transport
    Stdio->>MCPProcess: 8. Setup stdin/stdout handlers
    
    Note over Client,Stdio: Phase 5: MCP Handshake
    Client->>Stdio: 9. Send MCP initialize (JSON-RPC)
    Stdio->>MCPProcess: 10. Forward via stdin
    MCPProcess->>Stdio: 11. Response via stdout
    Stdio->>Client: 12. Parse and return response
    
    Note over Client,Stdio: Phase 6: Capability Discovery
    Client->>Stdio: 13. Request tools/list
    MCPProcess->>Stdio: 14. Available tools response
    Client->>Stdio: 15. Request resources/list
    MCPProcess->>Stdio: 16. Available resources response
    
    Note over Client,Stdio: Phase 7: Operational
    loop Tool Execution
        Client->>Stdio: JSON-RPC tool request
        MCPProcess->>Stdio: Tool execution result
    end
    
    loop Health Monitoring
        Client->>Stdio: Periodic health checks
        MCPProcess->>Stdio: Health status
    end
```

### Implementation Details

#### 1. Environment Preparation and Token Injection

```typescript
// src/electron/mcp/clients/local-service-client.ts
protected async prepareEnvironment(): Promise<Record<string, string>> {
  const env: Record<string, string> = {
    ...this.serverConfig.env,
    MCP_SERVER_NAME: this.serviceId,
    MCP_LOG_LEVEL: this.serverConfig.enableLogging ? 'info' : 'error'
  };

  // Securely inject OAuth tokens via environment variables
  if (this.oauthService) {
    try {
      const authStatus = await this.oauthService.getAuthStatus();
      if (authStatus.authenticated) {
        const accessToken = await this.oauthService.getAccessToken();
        env.OAUTH_ACCESS_TOKEN = accessToken;
        
        // Additional service-specific environment variables
        if (authStatus.expiresAt) {
          env.OAUTH_TOKEN_EXPIRY = authStatus.expiresAt.toISOString();
        }
        
        if (authStatus.scopes) {
          env.OAUTH_SCOPES = authStatus.scopes.join(',');
        }
      }
    } catch (error) {
      console.warn('Failed to get OAuth tokens for environment:', error);
    }
  }

  return env;
}
```

#### 2. Process Spawning and Management

```typescript
private async startServerProcess(): Promise<void> {
  if (this.serverProcess) {
    console.log('Server process already running');
    return;
  }

  console.log(`Starting MCP server process: ${this.serverConfig.executable}`);

  try {
    // Prepare secure environment with OAuth tokens
    const env = await this.prepareEnvironment();

    // Spawn the MCP server process with stdio pipes
    this.serverProcess = spawn(this.serverConfig.executable, this.serverConfig.args || [], {
      cwd: this.serverConfig.cwd || process.cwd(),
      env: {
        ...process.env,  // Inherit system environment
        ...env           // Add MCP-specific variables
      },
      stdio: ['pipe', 'pipe', 'pipe']  // stdin, stdout, stderr
    });

    // Set up comprehensive process event handling
    this.setupProcessEventHandlers();

    // Wait for process to stabilize before proceeding
    await this.waitForProcessReady();

    console.log(`✅ MCP server process started with PID: ${this.serverProcess.pid}`);
  } catch (error) {
    console.error('❌ Failed to start MCP server process:', error);
    throw error;
  }
}

private setupProcessEventHandlers(): void {
  if (!this.serverProcess) return;

  // Handle process lifecycle events
  this.serverProcess.on('spawn', () => {
    console.log(`MCP server process spawned (${this.serviceId})`);
    this.processStartTime = new Date();
  });

  this.serverProcess.on('exit', (code, signal) => {
    console.log(`MCP server process exited (${this.serviceId}): code=${code}, signal=${signal}`);
    this.handleProcessExit(code, signal);
  });

  this.serverProcess.on('error', (error) => {
    console.error(`MCP server process error (${this.serviceId}):`, error);
    this.handleProcessError(error);
  });

  // Handle stderr for logging and debugging
  this.serverProcess.stderr?.on('data', (data: Buffer) => {
    if (this.serverConfig.enableLogging) {
      console.log(`[${this.serviceId} stderr]:`, data.toString().trim());
    }
  });
}
```

#### 3. Stdio Transport Implementation

```typescript
// src/electron/mcp/transports/stdio-transport.ts
export class StdioTransport {
  private messageBuffer: string = '';
  private messageQueue: Map<string, PendingRequest> = new Map();
  private nextRequestId: number = 1;

  async initialize(): Promise<void> {
    console.log('Initializing stdio transport...');

    try {
      // Set up bidirectional communication handlers
      this.setupProcessHandlers();

      // Perform MCP protocol handshake
      await this.performHandshake();

      this.connectionStartTime = Date.now();
      this.initialized = true;
      console.log('✅ Stdio transport initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize stdio transport:', error);
      throw error;
    }
  }

  private setupProcessHandlers(): void {
    if (!this.process.stdout || !this.process.stderr || !this.process.stdin) {
      throw new Error('Process must have stdin, stdout, and stderr pipes');
    }

    // Handle stdout data (MCP protocol messages)
    this.process.stdout.on('data', (data: Buffer) => {
      this.handleStdoutData(data);
    });

    // Handle stderr data (logging and errors)
    this.process.stderr.on('data', (data: Buffer) => {
      if (this.config.enableLogging) {
        console.log(`[MCP Server stderr]: ${data.toString()}`);
      }
    });
  }

  private handleStdoutData(data: Buffer): void {
    this.messageBuffer += data.toString();
    
    // Process complete JSON-RPC messages (newline-delimited)
    const lines = this.messageBuffer.split('\n');
    this.messageBuffer = lines.pop() || ''; // Keep incomplete line in buffer

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine) {
        try {
          const message = JSON.parse(trimmedLine) as MCPMessage;
          this.processMessage(message);
        } catch (error) {
          console.error('Failed to parse MCP message:', trimmedLine, error);
        }
      }
    }
  }
```

#### 4. Message Processing and Request Correlation

```typescript
private processMessage(message: MCPMessage): void {
  if (message.id && this.messageQueue.has(String(message.id))) {
    // This is a response to a pending request
    const pendingRequest = this.messageQueue.get(String(message.id))!;
    clearTimeout(pendingRequest.timeout);
    this.messageQueue.delete(String(message.id));

    // Update performance metrics
    const responseTime = Date.now() - pendingRequest.timestamp;
    this.requestTimes.push(responseTime);
    this.updateAverageResponseTime();

    if (message.error) {
      pendingRequest.reject(new Error(`MCP Error ${message.error.code}: ${message.error.message}`));
    } else {
      pendingRequest.resolve(message as MCPResponse);
    }
  } else {
    // This is a notification or unsolicited message
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('Error in message handler:', error);
      }
    });
  }
}

async sendRequest(request: MCPRequest): Promise<MCPResponse> {
  if (!this.initialized) {
    throw new Error('Transport not initialized');
  }

  const startTime = Date.now();
  this.metrics.requestCount++;

  // Ensure request has a unique ID for correlation
  if (!request.id) {
    request.id = this.generateRequestId();
  }

  return new Promise((resolve, reject) => {
    // Set up timeout handling
    const timeout = setTimeout(() => {
      this.messageQueue.delete(String(request.id));
      this.metrics.errorCount++;
      reject(new Error(`Request timeout: ${request.id}`));
    }, this.config.timeout);

    // Queue the request for response correlation
    this.messageQueue.set(String(request.id), {
      resolve,
      reject,
      timeout,
      timestamp: startTime
    });

    try {
      // Send JSON-RPC message via stdin
      const message = JSON.stringify(request) + '\n';
      this.process.stdin!.write(message);
      
      if (this.config.enableLogging) {
        console.log(`📤 Sent MCP request: ${request.method} (${request.id})`);
      }
    } catch (error) {
      clearTimeout(timeout);
      this.messageQueue.delete(String(request.id));
      this.metrics.errorCount++;
      reject(error);
    }
  });
}
```

#### 5. MCP Protocol Handshake for Stdio

```typescript
private async performHandshake(): Promise<void> {
  console.log('Performing MCP handshake...');
  
  const initializeRequest: MCPRequest = {
    jsonrpc: "2.0",
    id: this.generateRequestId(),
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {
        tools: {},
        resources: {},
        prompts: {}
      },
      clientInfo: {
        name: "Alpine Intellect Desktop",
        version: "1.0.0"
      }
    }
  };

  try {
    const response = await this.sendRequest(initializeRequest);
    console.log('✅ MCP handshake completed:', response.result);
    
    // Store server capabilities for later use
    this.serverCapabilities = response.result;
  } catch (error) {
    console.error('❌ MCP handshake failed:', error);
    throw error;
  }
}
```

## Message Exchange Patterns

### Request/Response Pattern

Both transports implement the JSON-RPC 2.0 specification for request/response communication:

```typescript
// Standard MCP tool execution request
const toolRequest: MCPRequest = {
  jsonrpc: "2.0",
  id: "req_12345",
  method: "tools/call",
  params: {
    name: "get_file_content",
    arguments: {
      file_key: "figma_file_abc123",
      node_ids: ["node1", "node2"]
    }
  }
};

// Corresponding response
const toolResponse: MCPResponse = {
  jsonrpc: "2.0",
  id: "req_12345",
  result: {
    content: {
      nodes: [
        { id: "node1", type: "FRAME", name: "Header" },
        { id: "node2", type: "TEXT", name: "Title" }
      ]
    },
    metadata: {
      file_name: "Design System",
      last_modified: "2024-01-15T10:30:00Z"
    }
  }
};
```

### Notification Pattern (Stdio Only)

Stdio transport supports bidirectional notifications for real-time updates:

```typescript
// Server-initiated notification (no response expected)
const notification: MCPNotification = {
  jsonrpc: "2.0",
  method: "notifications/tools_changed",
  params: {
    added: ["new_export_tool"],
    removed: ["deprecated_tool"],
    modified: ["updated_search_tool"]
  }
};

// Client handles notifications asynchronously
transport.onMessage((message: MCPMessage) => {
  if (!message.id) {
    // This is a notification
    switch (message.method) {
      case 'notifications/tools_changed':
        this.refreshAvailableTools();
        break;
      case 'notifications/auth_expired':
        this.handleAuthExpiration();
        break;
    }
  }
});
```

### Error Handling Pattern

```typescript
// Error response format
const errorResponse: MCPResponse = {
  jsonrpc: "2.0",
  id: "req_12345",
  error: {
    code: -32602,
    message: "Invalid params",
    data: {
      field: "file_key",
      reason: "File not found or access denied"
    }
  }
};

// Client-side error handling
try {
  const result = await transport.sendRequest(toolRequest);
  return result.result;
} catch (error) {
  if (error.message.includes('MCP Error -32602')) {
    // Handle invalid parameters
    throw new Error('Invalid file key provided');
  } else if (error.message.includes('timeout')) {
    // Handle timeout
    throw new Error('Request timed out - server may be overloaded');
  }
  throw error;
}
```

## Connection Lifecycle Management

### Health Monitoring System

```typescript
// src/electron/mcp/clients/local-service-client.ts
private startHealthMonitoring(): void {
  // Perform health checks every 30 seconds
  this.healthCheckInterval = setInterval(async () => {
    try {
      await this.performHealthCheck();
    } catch (error) {
      console.warn(`Health check failed for ${this.serviceId}:`, error);
    }
  }, 30000);

  this.processStartTime = new Date();
  console.log(`Started health monitoring for ${this.serviceId}`);
}

private async performHealthCheck(): Promise<void> {
  if (this.isShuttingDown) return;

  this.lastHealthCheck = new Date();

  // Multi-layer health verification

  // 1. Process health check
  if (!this.serverProcess || this.serverProcess.killed || this.serverProcess.exitCode !== null) {
    console.warn(`Process health check failed for ${this.serviceId}: process not running`);
    await this.attemptRestart();
    return;
  }

  // 2. Transport health check
  if (!this.transport?.isHealthy()) {
    console.warn(`Transport health check failed for ${this.serviceId}`);
    return;
  }

  // 3. MCP server responsiveness check
  try {
    await this.transport.sendRequest({
      jsonrpc: "2.0",
      id: "health_ping",
      method: "ping",
      params: {}
    });
  } catch (error) {
    console.warn(`MCP server ping failed for ${this.serviceId}:`, error);
    // Consider restart if multiple consecutive failures
  }
}

getHealthMetrics(): HealthMetrics {
  return {
    serviceId: this.serviceId,
    processStartTime: this.processStartTime,
    lastHealthCheck: this.lastHealthCheck,
    restartCount: this.restartCount,
    isHealthy: this.isHealthy(),
    processId: this.serverProcess?.pid,
    uptime: this.processStartTime ? Date.now() - this.processStartTime.getTime() : 0,
    transportMetrics: this.transport?.getMetrics()
  };
}
```

### Automatic Recovery and Restart Logic

```typescript
private async handleProcessExit(code: number | null, signal: string | null): Promise<void> {
  console.log(`Process exit for ${this.serviceId}: code=${code}, signal=${signal}`);

  this.serverProcess = null;
  this.transport = null;

  if (!this.isShuttingDown && this.shouldRestart(code, signal)) {
    await this.attemptRestart();
  }
}

private shouldRestart(code: number | null, signal: string | null): boolean {
  // Don't restart if explicitly killed or max restarts exceeded
  if (signal === 'SIGKILL' || signal === 'SIGTERM') {
    return false;
  }

  if (this.restartCount >= (this.serverConfig.maxRestarts || 3)) {
    console.error(`Max restart attempts exceeded for ${this.serviceId}`);
    return false;
  }

  // Restart on unexpected exits (non-zero exit codes)
  return code !== 0;
}

private async attemptRestart(): Promise<void> {
  this.restartCount++;
  console.log(`Attempting restart ${this.restartCount} for ${this.serviceId}...`);

  try {
    // Exponential backoff for restart attempts
    const delay = Math.min(this.serverConfig.restartDelay * Math.pow(2, this.restartCount - 1), 30000);
    await new Promise(resolve => setTimeout(resolve, delay));

    // Clean up any remaining resources
    await this.cleanup();

    // Restart the service
    await this.initialize();

    console.log(`✅ Successfully restarted ${this.serviceId}`);
    this.restartCount = 0; // Reset counter on successful restart
  } catch (error) {
    console.error(`❌ Failed to restart ${this.serviceId}:`, error);
  }
}
```

### Graceful Shutdown Procedures

```typescript
// Stdio transport graceful shutdown
async close(): Promise<void> {
  console.log('Closing stdio transport...');

  // 1. Clear all pending requests with appropriate errors
  for (const [, pendingRequest] of this.messageQueue) {
    clearTimeout(pendingRequest.timeout);
    pendingRequest.reject(new Error('Transport closed'));
  }
  this.messageQueue.clear();

  // 2. Signal graceful shutdown to MCP server
  if (this.process.stdin && !this.process.stdin.destroyed) {
    try {
      // Send shutdown notification if server supports it
      const shutdownNotification: MCPNotification = {
        jsonrpc: "2.0",
        method: "notifications/shutdown",
        params: {}
      };
      this.process.stdin.write(JSON.stringify(shutdownNotification) + '\n');
    } catch (error) {
      console.warn('Failed to send shutdown notification:', error);
    }

    // Close stdin to signal end of communication
    this.process.stdin.end();
  }

  // 3. Wait for graceful shutdown with timeout
  await new Promise<void>((resolve) => {
    const gracefulTimeout = setTimeout(() => {
      if (!this.process.killed) {
        console.log('Force killing MCP server process...');
        this.process.kill('SIGKILL');
      }
      resolve();
    }, 5000); // 5 second grace period

    this.process.on('exit', () => {
      clearTimeout(gracefulTimeout);
      resolve();
    });
  });

  // 4. Notify close handlers
  this.closeHandlers.forEach(handler => {
    try {
      handler();
    } catch (error) {
      console.error('Error in close handler:', error);
    }
  });

  this.initialized = false;
  console.log('✅ Stdio transport closed');
}

// Service-level shutdown coordination
async shutdown(): Promise<void> {
  console.log(`Shutting down ${this.serviceId}...`);
  this.isShuttingDown = true;

  // Stop health monitoring
  if (this.healthCheckInterval) {
    clearInterval(this.healthCheckInterval);
    this.healthCheckInterval = null;
  }

  // Close transport and clean up resources
  await this.cleanup();

  console.log(`✅ ${this.serviceId} shutdown completed`);
}
```

## Security Considerations

### Token Management and Storage

#### Secure Token Storage
```typescript
// src/electron/mcp/auth/oauth-service.ts
export class OAuthService {
  private keychain: KeychainService;

  async storeTokens(tokens: TokenResponse): Promise<void> {
    // Store tokens in system keychain (macOS Keychain, Windows Credential Store, Linux Secret Service)
    await this.keychain.setPassword(
      `alpine-intellect-${this.serviceId}`,
      'access_token',
      tokens.access_token
    );

    if (tokens.refresh_token) {
      await this.keychain.setPassword(
        `alpine-intellect-${this.serviceId}`,
        'refresh_token',
        tokens.refresh_token
      );
    }

    // Store metadata in encrypted local storage
    await this.storeTokenMetadata({
      expiresAt: new Date(Date.now() + tokens.expires_in * 1000),
      scopes: tokens.scope?.split(' ') || [],
      tokenType: tokens.token_type || 'Bearer'
    });
  }

  async getAccessToken(): Promise<string> {
    // Check if token needs refresh
    const metadata = await this.getTokenMetadata();
    if (metadata.expiresAt && metadata.expiresAt <= new Date()) {
      await this.refreshAccessToken();
    }

    // Retrieve from secure storage
    return await this.keychain.getPassword(
      `alpine-intellect-${this.serviceId}`,
      'access_token'
    );
  }
}
```

#### Token Injection Security (Stdio Transport)
```typescript
// Secure environment variable injection
protected async prepareEnvironment(): Promise<Record<string, string>> {
  const env: Record<string, string> = {
    ...this.serverConfig.env,
    MCP_SERVER_NAME: this.serviceId,
    MCP_LOG_LEVEL: this.serverConfig.enableLogging ? 'info' : 'error'
  };

  if (this.oauthService) {
    try {
      const authStatus = await this.oauthService.getAuthStatus();
      if (authStatus.authenticated) {
        // Tokens are injected only into the specific process environment
        // They never touch the filesystem or global environment
        const accessToken = await this.oauthService.getAccessToken();
        env.OAUTH_ACCESS_TOKEN = accessToken;

        // Additional security metadata
        env.OAUTH_TOKEN_TYPE = 'Bearer';
        env.OAUTH_SCOPES = authStatus.scopes?.join(',') || '';

        // Token expiry for server-side validation
        if (authStatus.expiresAt) {
          env.OAUTH_TOKEN_EXPIRY = authStatus.expiresAt.toISOString();
        }
      }
    } catch (error) {
      console.warn('Failed to get OAuth tokens for environment:', error);
      // Fail securely - don't start process without proper authentication
      throw new Error('Authentication required but tokens unavailable');
    }
  }

  return env;
}
```

### Process Isolation Techniques

#### Sandboxing and Resource Limits
```typescript
// Enhanced process spawning with security constraints
private async startServerProcess(): Promise<void> {
  const env = await this.prepareEnvironment();

  this.serverProcess = spawn(this.serverConfig.executable, this.serverConfig.args || [], {
    cwd: this.serverConfig.cwd || process.cwd(),
    env: {
      // Minimal environment inheritance
      PATH: process.env.PATH,
      HOME: process.env.HOME,
      // MCP-specific variables only
      ...env
    },
    stdio: ['pipe', 'pipe', 'pipe'],

    // Security options (Node.js specific)
    detached: false,  // Keep process attached to parent
    shell: false,     // Prevent shell injection

    // Resource limits (if supported by platform)
    uid: process.getuid?.(),  // Run as current user
    gid: process.getgid?.(),  // Run as current group
  });

  // Set additional security constraints post-spawn
  if (this.serverProcess.pid) {
    try {
      // Set process priority (lower priority for MCP servers)
      process.setpriority?.(this.serverProcess.pid, 10);
    } catch (error) {
      console.warn('Failed to set process priority:', error);
    }
  }
}
```

#### Communication Channel Security
```typescript
// Stdio pipes are inherently secure for local communication
// - No network exposure
// - Process-local file descriptors
// - Automatic cleanup on process termination
// - No intermediate files or sockets

private setupProcessHandlers(): void {
  // Validate all incoming data
  this.process.stdout?.on('data', (data: Buffer) => {
    // Limit message size to prevent DoS
    if (data.length > this.MAX_MESSAGE_SIZE) {
      console.error('Received oversized message, terminating connection');
      this.process.kill('SIGTERM');
      return;
    }

    this.handleStdoutData(data);
  });

  // Monitor stderr for security events
  this.process.stderr?.on('data', (data: Buffer) => {
    const message = data.toString();

    // Log security-relevant events
    if (message.includes('authentication') || message.includes('token')) {
      console.warn(`Security event in ${this.serviceId}:`, message);
    }
  });
}
```

### Secure Communication Channels

#### HTTP/SSE Transport Security
```typescript
// TLS and certificate validation
async sendRequest(request: MCPRequest): Promise<MCPResponse> {
  const accessToken = await this.config.getAccessToken();

  const response = await fetch(this.config.httpEndpoint, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
      'User-Agent': 'Alpine-Intellect-Desktop/1.0.0',
      // Security headers
      'X-Requested-With': 'XMLHttpRequest',
      'Cache-Control': 'no-cache'
    },
    body: JSON.stringify(request),

    // Security options
    credentials: 'omit',  // Don't send cookies
    mode: 'cors',        // Enforce CORS
    redirect: 'error'    // Don't follow redirects
  });

  // Validate response
  if (!response.ok) {
    // Don't leak sensitive information in error messages
    throw new Error(`Request failed with status ${response.status}`);
  }

  return await response.json();
}
```

## Performance Characteristics

### Latency Comparison

| Transport | Connection Setup | Request Latency | Throughput | Use Case |
|-----------|------------------|-----------------|------------|----------|
| HTTP/SSE  | 100-500ms | 50-200ms | 100-1000 req/s | Remote APIs, cloud services |
| Stdio     | 10-50ms | 1-10ms | 1000-10000 req/s | Local tools, real-time processing |

### HTTP/SSE Transport Performance

```typescript
// Performance monitoring for HTTP transport
export class HTTPSSETransport {
  private metrics: TransportMetrics = {
    requestCount: 0,
    errorCount: 0,
    averageResponseTime: 0,
    connectionUptime: 0
  };

  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    const startTime = performance.now();
    this.metrics.requestCount++;

    try {
      const response = await this.performRequest(request);

      // Update performance metrics
      const responseTime = performance.now() - startTime;
      this.updateResponseTimeMetrics(responseTime);

      return response;
    } catch (error) {
      this.metrics.errorCount++;
      throw error;
    }
  }

  private updateResponseTimeMetrics(responseTime: number): void {
    // Exponential moving average for response time
    const alpha = 0.1;
    this.metrics.averageResponseTime =
      (alpha * responseTime) + ((1 - alpha) * this.metrics.averageResponseTime);
  }

  getPerformanceReport(): PerformanceReport {
    return {
      transport: 'http-sse',
      metrics: this.metrics,
      healthStatus: this.isHealthy(),
      recommendations: this.generatePerformanceRecommendations()
    };
  }

  private generatePerformanceRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.metrics.averageResponseTime > 1000) {
      recommendations.push('High latency detected - consider caching or local alternatives');
    }

    if (this.metrics.errorCount / this.metrics.requestCount > 0.05) {
      recommendations.push('High error rate - check network connectivity and server health');
    }

    return recommendations;
  }
}
```

### Stdio Transport Performance

```typescript
// High-performance stdio transport with optimizations
export class StdioTransport {
  private requestTimes: number[] = [];
  private messageBuffer: string = '';

  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    const startTime = performance.now();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(String(request.id));
        reject(new Error(`Request timeout: ${request.id}`));
      }, this.config.timeout);

      this.messageQueue.set(String(request.id), {
        resolve: (response) => {
          // Record performance metrics
          const responseTime = performance.now() - startTime;
          this.recordResponseTime(responseTime);
          resolve(response);
        },
        reject,
        timeout,
        timestamp: startTime
      });

      // Optimized message serialization
      const message = JSON.stringify(request) + '\n';
      this.process.stdin!.write(message, 'utf8');
    });
  }

  private recordResponseTime(responseTime: number): void {
    this.requestTimes.push(responseTime);

    // Keep only recent measurements for accurate averages
    if (this.requestTimes.length > 1000) {
      this.requestTimes.shift();
    }
  }

  getPerformanceMetrics(): StdioPerformanceMetrics {
    const times = this.requestTimes;

    return {
      averageResponseTime: times.reduce((a, b) => a + b, 0) / times.length,
      medianResponseTime: this.calculateMedian(times),
      p95ResponseTime: this.calculatePercentile(times, 0.95),
      p99ResponseTime: this.calculatePercentile(times, 0.99),
      requestsPerSecond: this.calculateThroughput(),
      memoryUsage: process.memoryUsage(),
      processUptime: Date.now() - this.connectionStartTime
    };
  }

  private calculateThroughput(): number {
    const recentRequests = this.requestTimes.filter(
      time => time > Date.now() - 60000 // Last minute
    );
    return recentRequests.length / 60; // Requests per second
  }
}
```

### Resource Usage and Scalability

#### Memory Management
```typescript
// Memory-efficient message handling
private handleStdoutData(data: Buffer): void {
  // Prevent memory leaks from large message buffers
  if (this.messageBuffer.length > this.MAX_BUFFER_SIZE) {
    console.error('Message buffer overflow, resetting');
    this.messageBuffer = '';
    return;
  }

  this.messageBuffer += data.toString();

  // Process messages immediately to free memory
  const lines = this.messageBuffer.split('\n');
  this.messageBuffer = lines.pop() || '';

  for (const line of lines) {
    if (line.trim()) {
      this.processMessageLine(line);
    }
  }
}

// Connection pooling for HTTP transport
class ConnectionPool {
  private connections: Map<string, Connection> = new Map();
  private maxConnections = 10;

  async getConnection(endpoint: string): Promise<Connection> {
    if (this.connections.size >= this.maxConnections) {
      // Implement LRU eviction
      this.evictLeastRecentlyUsed();
    }

    let connection = this.connections.get(endpoint);
    if (!connection || !connection.isHealthy()) {
      connection = await this.createConnection(endpoint);
      this.connections.set(endpoint, connection);
    }

    return connection;
  }
}
```

### Scalability Considerations

#### Horizontal Scaling (HTTP/SSE)
- **Load Balancing**: Multiple server instances behind load balancer
- **Connection Pooling**: Reuse HTTP connections across requests
- **Caching**: Response caching for frequently accessed data
- **Rate Limiting**: Client-side rate limiting to prevent server overload

#### Vertical Scaling (Stdio)
- **Process Limits**: Maximum number of concurrent local processes
- **Resource Monitoring**: CPU and memory usage tracking
- **Queue Management**: Request queuing during high load
- **Process Recycling**: Restart processes periodically to prevent memory leaks

```typescript
// Resource-aware process management
class ProcessManager {
  private activeProcesses: Map<string, LocalServiceClient> = new Map();
  private maxProcesses = 5;
  private resourceMonitor: ResourceMonitor;

  async startService(serviceId: string, config: ServerConfiguration): Promise<LocalServiceClient> {
    // Check resource availability
    const resources = await this.resourceMonitor.getCurrentUsage();
    if (resources.memoryUsage > 0.8 || resources.cpuUsage > 0.9) {
      throw new Error('Insufficient resources to start new MCP server process');
    }

    // Enforce process limits
    if (this.activeProcesses.size >= this.maxProcesses) {
      await this.recycleOldestProcess();
    }

    const service = new LocalServiceClient(config);
    await service.initialize();

    this.activeProcesses.set(serviceId, service);
    return service;
  }

  private async recycleOldestProcess(): Promise<void> {
    // Find least recently used process
    let oldestService: LocalServiceClient | null = null;
    let oldestTime = Date.now();

    for (const service of this.activeProcesses.values()) {
      const metrics = service.getHealthMetrics();
      if (metrics.processStartTime && metrics.processStartTime.getTime() < oldestTime) {
        oldestTime = metrics.processStartTime.getTime();
        oldestService = service;
      }
    }

    if (oldestService) {
      await oldestService.shutdown();
      this.activeProcesses.delete(oldestService.serviceId);
    }
  }
}
```

This comprehensive guide provides developers with the technical knowledge needed to understand, implement, and optimize MCP client-server connections in the Alpine Intellect Desktop App architecture.
```
```

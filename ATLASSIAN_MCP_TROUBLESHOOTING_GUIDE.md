# Atlassian MCP Service Troubleshooting Guide

## Issue: Atlassian Service Not Appearing in UI

The Atlassian service is not visible in the MCP Services section of the Alpine Intellect UI, despite the implementation being complete.

## Root Cause Analysis

Based on the code analysis, the most likely causes are:

### 1. Missing Environment Variable
**Issue**: `ATLASSIAN_CLIENT_ID` environment variable is not set
**Evidence**: Configuration manager logs warning when env var not found
**Impact**: Service may be disabled or fail to initialize

### 2. Service Initialization Failure
**Issue**: Service fails during initialization due to missing OAuth configuration
**Evidence**: Service registry creates services but may fail during auth setup
**Impact**: Service exists but is marked as unhealthy/unauthenticated

### 3. UI Component Missing (FIXED)
**Issue**: No AtlassianServiceCard component and missing UI rendering logic
**Status**: ✅ FIXED - Created AtlassianServiceCard and updated MCPDashboard

## Troubleshooting Steps

### Step 1: Check Environment Variables
```bash
# Check if ATLASSIAN_CLIENT_ID is set
echo $ATLASSIAN_CLIENT_ID

# If not set, add to your environment
export ATLASSIAN_CLIENT_ID=your_atlassian_client_id_here

# For permanent setup, add to your shell profile
echo 'export ATLASSIAN_CLIENT_ID=your_atlassian_client_id_here' >> ~/.bashrc
# or ~/.zshrc depending on your shell
```

### Step 2: Check MCP Configuration Loading
Open the Electron app and check the console logs for:

```
📋 Loaded configuration with servers: github, figma, atlassian
📊 Server configuration summary:
   All servers: github, figma, atlassian
   Enabled servers: github, figma, atlassian
```

If you see warnings like:
```
Environment variable not found: ATLASSIAN_CLIENT_ID, using placeholder
```

This confirms the environment variable issue.

### Step 3: Check Service Registry Initialization
Look for these logs in the console:
```
Creating and registering service: atlassian
✅ Service atlassian registered successfully
```

If you see errors like:
```
❌ Failed to create service atlassian: [error details]
```

This indicates a service creation issue.

### Step 4: Check Service Status via API
Open the browser developer tools and run:

```javascript
// Check if Atlassian service is loaded
window.api.mcpGetAllServicesStatus().then(result => {
  console.log('All services:', result);
  const atlassianService = result.statuses?.find(s => s.serviceId === 'atlassian');
  console.log('Atlassian service:', atlassianService);
});

// Check specific service status
window.api.mcpGetServiceStatus('atlassian').then(result => {
  console.log('Atlassian service status:', result);
});
```

### Step 5: Check MCP Host Stats
```javascript
window.api.mcpGetHostStats().then(result => {
  console.log('MCP Host stats:', result);
  console.log('Total services:', result.stats?.serviceStats.totalServices);
  console.log('Enabled services:', result.stats?.serviceStats.enabledServices);
});
```

## Expected Results

### With Correct Setup
1. **Environment Variable**: `ATLASSIAN_CLIENT_ID` should be set
2. **Configuration Loading**: Should see "atlassian" in loaded servers list
3. **Service Creation**: Should see successful service registration
4. **Service Status**: Should return service object with `enabled: true`
5. **UI Display**: AtlassianServiceCard should appear in MCP Services section

### Service Status Object
```javascript
{
  serviceId: 'atlassian',
  displayName: 'Atlassian',
  enabled: true,
  authenticated: false, // Initially false until OAuth completed
  healthy: true,
  authStatus: {
    authenticated: false,
    scopes: [/* Atlassian scopes */]
  },
  availableTools: [
    'create_jira_issue',
    'search_jira_issues',
    'create_confluence_page',
    // ... other tools
  ],
  endpoints: {
    http: 'https://api.atlassian.com/mcp/v1',
    sse: 'https://api.atlassian.com/mcp/v1/events',
    discovery: 'https://api.atlassian.com/mcp/.well-known/oauth-authorization-server'
  }
}
```

## Quick Fix Commands

### 1. Set Environment Variable (macOS/Linux)
```bash
# Temporary (current session only)
export ATLASSIAN_CLIENT_ID=your_client_id_here

# Permanent (add to shell profile)
echo 'export ATLASSIAN_CLIENT_ID=your_client_id_here' >> ~/.zshrc
source ~/.zshrc
```

### 2. Set Environment Variable (Windows)
```cmd
# Temporary (current session only)
set ATLASSIAN_CLIENT_ID=your_client_id_here

# Permanent (system environment variable)
setx ATLASSIAN_CLIENT_ID "your_client_id_here"
```

### 3. Restart Application
After setting the environment variable, restart the Alpine Intellect application completely:
1. Quit the application
2. Restart from terminal/command prompt to ensure env vars are loaded
3. Check MCP Services section

### 4. Force Configuration Reload
If the service still doesn't appear, try:
```javascript
// In browser dev tools
window.api.mcpGetHostStats().then(() => {
  // Refresh the page to reload services
  window.location.reload();
});
```

## Verification Checklist

- [ ] `ATLASSIAN_CLIENT_ID` environment variable is set
- [ ] Application restarted after setting environment variable
- [ ] Console shows "atlassian" in loaded servers list
- [ ] No environment variable warnings in console
- [ ] Service registry shows successful Atlassian service creation
- [ ] `mcpGetAllServicesStatus()` returns Atlassian service
- [ ] AtlassianServiceCard appears in UI
- [ ] Service shows as enabled but not authenticated (expected initially)

## Next Steps After Service Appears

1. **Register Atlassian OAuth App**
   - Go to Atlassian Developer Console
   - Create new OAuth 2.0 app
   - Set redirect URI: `thealpinecode.alpineintellect://auth-callback/atlassian`
   - Get client ID and secret

2. **Store Client Secret**
   ```javascript
   // This will be handled by the OAuth flow, but client secret needs to be stored in Keytar
   // The OAuth flow will prompt for this
   ```

3. **Test OAuth Flow**
   - Click "Connect to Atlassian" button
   - Complete OAuth authorization
   - Verify service shows as authenticated

4. **Test Tools**
   - Try creating a Jira issue
   - Try searching Confluence content
   - Try creating a Bitbucket repository

## Common Issues and Solutions

### Issue: Service appears but shows as "Unhealthy"
**Solution**: Check OAuth configuration and client secret storage

### Issue: OAuth flow fails
**Solution**: Verify redirect URI matches exactly in Atlassian OAuth app

### Issue: Tools fail to execute
**Solution**: Check token scopes and accessible resources

### Issue: Real-time notifications not working
**Solution**: Check SSE endpoint connectivity and authentication

## Support Information

If the service still doesn't appear after following these steps:

1. **Collect Logs**: Copy all console output from Electron app
2. **Check Environment**: Verify all environment variables are set correctly
3. **Verify Configuration**: Ensure `mcp-config.json` contains Atlassian configuration
4. **Test API Calls**: Use browser dev tools to test MCP API calls directly

The implementation is complete and should work once the environment variable is properly set and the application is restarted.

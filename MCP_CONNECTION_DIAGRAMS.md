# MCP Connection Establishment Diagrams

This document provides visual representations of the MCP connection establishment processes described in the main technical guide.

## System Architecture Overview

```mermaid
graph TB
    subgraph "Alpine Intellect Desktop App (MCP Client)"
        A[Main Application] --> B[Service Registry]
        B --> C[OAuth Service]
        B --> D[Configuration Manager]
        B --> E[HTTP/SSE Transport]
        B --> F[Stdio Transport]
        
        subgraph "Service Clients"
            G[GitHub Service]
            H[Figma Local Service]
            I[Other Services]
        end
        
        E --> G
        F --> H
        F --> I
    end
    
    subgraph "External Services"
        J[GitHub MCP Server]
        K[Other Remote Servers]
    end
    
    subgraph "Local Processes"
        L[Figma MCP Process]
        M[Other Local Processes]
    end
    
    G -.->|HTTPS/SSE| J
    G -.->|HTTPS/SSE| K
    H -->|stdio| L
    I -->|stdio| M
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style E fill:#fff3e0
    style F fill:#e8f5e8
    style J fill:#ffebee
    style L fill:#f1f8e9
```

## HTTP/SSE Connection Flow

```mermaid
sequenceDiagram
    participant App as Alpine Intellect App
    participant OAuth as OAuth Service
    participant Config as Config Manager
    participant Transport as HTTP/SSE Transport
    participant Server as Remote MCP Server
    participant SSE as SSE Endpoint

    Note over App,SSE: Phase 1: Service Initialization
    App->>Config: Load server configuration
    Config->>App: Return HTTP/SSE config
    App->>OAuth: Initialize OAuth service
    
    Note over App,SSE: Phase 2: Authentication
    App->>OAuth: Check authentication status
    alt Not authenticated
        OAuth->>App: Generate PKCE challenge
        OAuth->>App: Return authorization URL
        App->>OAuth: User completes OAuth flow
        OAuth->>Server: Exchange code for tokens (PKCE)
        Server->>OAuth: Return access & refresh tokens
        OAuth->>App: Store tokens securely
    else Already authenticated
        OAuth->>App: Return cached token status
    end
    
    Note over App,SSE: Phase 3: Transport Setup
    App->>Transport: Initialize with config
    Transport->>Server: Health check request
    Server->>Transport: Health status response
    Transport->>SSE: Establish SSE connection
    SSE->>Transport: Connection established
    
    Note over App,SSE: Phase 4: MCP Handshake
    Transport->>Server: MCP initialize request
    Server->>Transport: Server capabilities response
    Transport->>Server: Discover tools/resources
    Server->>Transport: Available tools list
    Transport->>App: Ready for operations
    
    Note over App,SSE: Phase 5: Operational
    loop Tool Execution
        App->>Transport: Tool execution request
        Transport->>Server: HTTP POST /tools/call
        Server->>Transport: Tool result
        Transport->>App: Return result
    end
    
    loop Real-time Events
        SSE->>Transport: Server-sent events
        Transport->>App: Event notifications
    end
```

## Stdio Connection Flow

```mermaid
sequenceDiagram
    participant App as Alpine Intellect App
    participant OAuth as OAuth Service
    participant LocalClient as Local Service Client
    participant ProcessMgr as Process Manager
    participant MCPProcess as MCP Server Process
    participant Stdio as Stdio Transport

    Note over App,Stdio: Phase 1: Pre-initialization
    App->>LocalClient: Initialize Figma local service
    LocalClient->>OAuth: Check authentication status
    OAuth->>LocalClient: Return token status
    
    Note over App,Stdio: Phase 2: Environment Preparation
    LocalClient->>OAuth: Get access token
    OAuth->>LocalClient: Return secure token
    LocalClient->>ProcessMgr: Prepare environment variables
    Note over ProcessMgr: Inject: ACCESS_TOKEN, CLIENT_ID, etc.
    
    Note over App,Stdio: Phase 3: Process Spawning
    ProcessMgr->>MCPProcess: spawn(node, [figma-mcp-server.js], {env})
    MCPProcess->>ProcessMgr: Process started (PID)
    ProcessMgr->>LocalClient: Process ready
    
    Note over App,Stdio: Phase 4: Transport Initialization
    LocalClient->>Stdio: Initialize stdio transport
    Stdio->>MCPProcess: Setup stdin/stdout handlers
    MCPProcess->>Stdio: Process ready signal
    
    Note over App,Stdio: Phase 5: MCP Handshake
    Stdio->>MCPProcess: Send MCP initialize (JSON-RPC)
    MCPProcess->>Stdio: Server capabilities response
    Stdio->>LocalClient: Handshake complete
    
    Note over App,Stdio: Phase 6: Tool Discovery
    LocalClient->>Stdio: Request tools/list
    Stdio->>MCPProcess: Forward request via stdin
    MCPProcess->>Stdio: Tools list via stdout
    Stdio->>LocalClient: Parse and return tools
    
    Note over App,Stdio: Phase 7: Health Monitoring Setup
    LocalClient->>LocalClient: Start health monitoring timer
    
    Note over App,Stdio: Phase 8: Operational
    loop Tool Execution
        App->>LocalClient: Execute tool request
        LocalClient->>Stdio: JSON-RPC request
        Stdio->>MCPProcess: Forward via stdin
        MCPProcess->>Stdio: Result via stdout
        Stdio->>LocalClient: Parse response
        LocalClient->>App: Return result
    end
    
    loop Health Checks
        LocalClient->>Stdio: Health ping
        Stdio->>MCPProcess: Ping request
        MCPProcess->>Stdio: Pong response
        Stdio->>LocalClient: Health status
    end
    
    loop Process Monitoring
        LocalClient->>ProcessMgr: Check process status
        ProcessMgr->>LocalClient: Process metrics
    end
```

## OAuth 2.0 with PKCE Flow

```mermaid
sequenceDiagram
    participant User as User
    participant App as Desktop App
    participant OAuth as OAuth Service
    participant Browser as System Browser
    participant AuthServer as Authorization Server
    participant Keychain as System Keychain

    Note over User,Keychain: PKCE Flow for Secure Authentication
    
    App->>OAuth: Initiate authentication
    OAuth->>OAuth: Generate code_verifier (random)
    OAuth->>OAuth: Generate code_challenge (SHA256)
    OAuth->>Browser: Open authorization URL with PKCE params
    
    Browser->>AuthServer: Authorization request + code_challenge
    AuthServer->>User: Show consent screen
    User->>AuthServer: Grant permission
    AuthServer->>Browser: Redirect with authorization code
    Browser->>App: Deep link callback with code
    
    App->>OAuth: Handle callback with auth code
    OAuth->>AuthServer: Exchange code for tokens + code_verifier
    AuthServer->>OAuth: Validate PKCE + return tokens
    OAuth->>Keychain: Store tokens securely
    OAuth->>App: Authentication complete
    
    Note over User,Keychain: Token Usage and Refresh
    
    loop API Requests
        App->>OAuth: Get access token
        OAuth->>Keychain: Retrieve token
        alt Token expired
            OAuth->>AuthServer: Refresh token request
            AuthServer->>OAuth: New access token
            OAuth->>Keychain: Update stored token
        end
        OAuth->>App: Return valid token
    end
```

## Process Lifecycle Management

```mermaid
stateDiagram-v2
    [*] --> Initializing: Start service
    
    Initializing --> Authenticating: Config loaded
    Authenticating --> SpawningProcess: OAuth complete
    Authenticating --> Failed: Auth failed
    
    SpawningProcess --> ProcessStarted: Process spawned
    SpawningProcess --> Failed: Spawn failed
    
    ProcessStarted --> HandshakeInProgress: Stdio connected
    ProcessStarted --> Failed: Connection failed
    
    HandshakeInProgress --> DiscoveringTools: MCP handshake complete
    HandshakeInProgress --> Failed: Handshake failed
    
    DiscoveringTools --> Operational: Tools discovered
    DiscoveringTools --> Failed: Discovery failed
    
    Operational --> HealthCheck: Periodic monitoring
    HealthCheck --> Operational: Healthy
    HealthCheck --> Restarting: Unhealthy
    
    Operational --> Shutting: Manual shutdown
    Operational --> ProcessCrashed: Process exit
    
    ProcessCrashed --> Restarting: Auto-restart enabled
    ProcessCrashed --> Failed: Max restarts exceeded
    
    Restarting --> SpawningProcess: Restart attempt
    Restarting --> Failed: Restart failed
    
    Shutting --> Cleanup: Graceful shutdown
    Cleanup --> [*]: Resources cleaned
    
    Failed --> [*]: Error state
    
    note right of Operational
        Health monitoring:
        - Process status
        - Transport health
        - MCP ping/pong
    end note
    
    note right of Restarting
        Restart logic:
        - Exponential backoff
        - Max retry limit
        - Clean environment
    end note
```

## Message Flow Patterns

```mermaid
graph LR
    subgraph "Request/Response Pattern"
        A[Client Request] --> B[Transport Layer]
        B --> C[MCP Server]
        C --> D[Server Response]
        D --> B
        B --> E[Client Response]
    end
    
    subgraph "Notification Pattern (Stdio Only)"
        F[Server Event] --> G[Notification]
        G --> H[Transport Layer]
        H --> I[Client Handler]
    end
    
    subgraph "Error Handling"
        J[Request] --> K{Server Available?}
        K -->|Yes| L[Process Request]
        K -->|No| M[Error Response]
        L --> N{Request Valid?}
        N -->|Yes| O[Success Response]
        N -->|No| P[Error Response]
    end
    
    style A fill:#e3f2fd
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style I fill:#f3e5f5
    style M fill:#ffebee
    style P fill:#ffebee
```

## Security Architecture

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Authentication Layer"
            A[OAuth 2.0 + PKCE]
            B[System Keychain Storage]
            C[Token Refresh Logic]
        end
        
        subgraph "Transport Security"
            D[HTTPS/TLS for Remote]
            E[Process Isolation for Local]
            F[Environment Variable Injection]
        end
        
        subgraph "Process Security"
            G[Minimal Environment]
            H[Resource Limits]
            I[Process Monitoring]
        end
        
        subgraph "Data Security"
            J[No Token Persistence to Disk]
            K[Secure Memory Handling]
            L[Graceful Cleanup]
        end
    end
    
    A --> D
    A --> E
    B --> F
    E --> G
    F --> J
    G --> H
    H --> I
    I --> L
    
    style A fill:#e1f5fe
    style D fill:#e8f5e8
    style E fill:#e8f5e8
    style J fill:#fff3e0
```

## Performance Comparison

```mermaid
graph LR
    subgraph "HTTP/SSE Transport"
        A[Connection Setup: 100-500ms]
        B[Request Latency: 50-200ms]
        C[Throughput: 100-1000 req/s]
        D[Network Dependent]
    end
    
    subgraph "Stdio Transport"
        E[Connection Setup: 10-50ms]
        F[Request Latency: 1-10ms]
        G[Throughput: 1000-10000 req/s]
        H[Process Local]
    end
    
    subgraph "Use Cases"
        I[Remote APIs]
        J[Cloud Services]
        K[Local Tools]
        L[Real-time Processing]
    end
    
    A --> I
    B --> J
    E --> K
    F --> L
    
    style A fill:#ffebee
    style B fill:#ffebee
    style E fill:#e8f5e8
    style F fill:#e8f5e8
```

These diagrams provide visual representations of the complex connection establishment processes, making it easier for developers to understand the flow and relationships between different components in the MCP system.

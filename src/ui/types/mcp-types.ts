// Global TypeScript interfaces for MCP functionality accessible to frontend

export interface MCPServiceStatus {
  serviceId: string;
  displayName: string;
  enabled: boolean;
  authenticated: boolean;
  healthy: boolean;
  authStatus: {
    authenticated: boolean;
    expiresAt?: Date;
    scopes?: string[];
    error?: string;
  };
  availableTools: string[];
  endpoints: {
    http: string;
    sse: string;
    discovery: string;
  };
}

export interface MCPAuthResult {
  success: boolean;
  authUrl?: string;
  error?: string;
}

export interface MCPToolResult {
  success: boolean;
  result?: {
    content: Array<{
      type: 'text' | 'image' | 'resource';
      text?: string;
      data?: string;
      mimeType?: string;
    }>;
    isError?: boolean;
  };
  error?: string;
}

export interface MCPHostStats {
  uptime: number;
  configPath: string;
  discoveryEnabled: boolean;
  serviceStats: {
    totalServices: number;
    enabledServices: number;
    authenticatedServices: number;
    healthyServices: number;
    services: Array<{
      serviceId: string;
      displayName: string;
      enabled: boolean;
      authenticated: boolean;
      healthy: boolean;
      lastError?: string;
    }>;
  };
  cacheStats: {
    size: number;
    maxEntries: number;
    ttl: number;
    oldestEntry?: Date;
    newestEntry?: Date;
  };
}

export interface MCPHealthCheck {
  healthy: boolean;
  services: Array<{
    serviceId: string;
    healthy: boolean;
    authenticated: boolean;
    error?: string;
  }>;
}

export interface MCPOAuthCallbackData {
  url: string;
  error?: string;
}

export interface MCPServiceConfig {
  serviceId: string;
  displayName: string;
  description: string;
  iconUrl?: string;
  scopes: string[];
  tools: Array<{
    name: string;
    description: string;
    parameters?: Record<string, any>;
  }>;
}

// GitHub-specific types
export interface GitHubToolParams {
  createRepository: {
    name: string;
    description?: string;
    private?: boolean;
    auto_init?: boolean;
  };
  createIssue: {
    owner: string;
    repo: string;
    title: string;
    body?: string;
    labels?: string[];
    assignees?: string[];
  };
  searchCode: {
    query: string;
    sort?: 'indexed' | 'updated';
    order?: 'asc' | 'desc';
    per_page?: number;
    page?: number;
  };
  getFileContent: {
    owner: string;
    repo: string;
    path: string;
    ref?: string;
  };
}

// Figma-specific types
export interface FigmaToolParams {
  getFileContent: {
    file_key: string;
    version?: string;
    ids?: string[];
    depth?: number;
    geometry?: 'paths' | 'bounds';
  };
  exportAssets: {
    file_key: string;
    ids: string[];
    format: 'jpg' | 'png' | 'svg' | 'pdf';
    scale?: number;
  };
  createComponent: {
    file_key: string;
    node_id: string;
    name: string;
    description?: string;
  };
  getTeamProjects: {
    team_id: string;
  };
}

// Atlassian-specific types
export interface AtlassianToolParams {
  // Jira tools
  createJiraIssue: {
    cloudId: string;
    projectKey: string;
    summary: string;
    description?: string;
    issueType: string;
    priority?: string;
    assignee?: string;
    labels?: string[];
    components?: string[];
  };
  updateJiraIssue: {
    cloudId: string;
    issueIdOrKey: string;
    summary?: string;
    description?: string;
    assignee?: string;
    priority?: string;
    status?: string;
    labels?: string[];
    components?: string[];
  };
  searchJiraIssues: {
    cloudId: string;
    jql: string;
    startAt?: number;
    maxResults?: number;
    fields?: string[];
    expand?: string[];
  };
  getJiraIssue: {
    cloudId: string;
    issueIdOrKey: string;
    fields?: string[];
    expand?: string[];
    properties?: string[];
  };
  // Confluence tools
  createConfluencePage: {
    cloudId: string;
    spaceKey: string;
    title: string;
    body: string;
    parentId?: string;
    type?: 'page' | 'blogpost';
    representation?: 'storage' | 'wiki' | 'view';
  };
  updateConfluencePage: {
    cloudId: string;
    pageId: string;
    title?: string;
    body?: string;
    version: number;
    representation?: 'storage' | 'wiki' | 'view';
  };
  searchConfluenceContent: {
    cloudId: string;
    cql: string;
    start?: number;
    limit?: number;
    includeArchivedSpaces?: boolean;
    expand?: string[];
  };
  getConfluencePage: {
    cloudId: string;
    pageId: string;
    expand?: string[];
    version?: number;
  };
  // Bitbucket tools
  createBitbucketRepository: {
    workspace: string;
    name: string;
    description?: string;
    is_private?: boolean;
    language?: string;
    has_issues?: boolean;
    has_wiki?: boolean;
    fork_policy?: 'allow_forks' | 'no_public_forks' | 'no_forks';
    project?: {
      key: string;
    };
  };
  createBitbucketPullRequest: {
    workspace: string;
    repo_slug: string;
    title: string;
    description?: string;
    source: {
      branch: {
        name: string;
      };
    };
    destination: {
      branch: {
        name: string;
      };
    };
    reviewers?: Array<{
      uuid: string;
    }>;
    close_source_branch?: boolean;
  };
  searchBitbucketCode: {
    workspace: string;
    repo_slug: string;
    search_query: string;
    page?: number;
    pagelen?: number;
  };
  getBitbucketFileContent: {
    workspace: string;
    repo_slug: string;
    commit: string;
    path: string;
    format?: 'raw' | 'rendered';
  };
  // Resource discovery
  listAccessibleResources: {};
}

// Service-specific configurations
export const MCP_SERVICE_CONFIGS: Record<string, MCPServiceConfig> = {
  github: {
    serviceId: 'github',
    displayName: 'GitHub',
    description: 'Connect to GitHub for repository management, issue tracking, and code collaboration',
    iconUrl: '/icons/github.svg',
    scopes: ['repo', 'user', 'read:org'],
    tools: [
      {
        name: 'create_repository',
        description: 'Create a new GitHub repository',
        parameters: {
          name: { type: 'string', required: true },
          description: { type: 'string', required: false },
          private: { type: 'boolean', required: false }
        }
      },
      {
        name: 'create_issue',
        description: 'Create a new issue in a repository',
        parameters: {
          owner: { type: 'string', required: true },
          repo: { type: 'string', required: true },
          title: { type: 'string', required: true },
          body: { type: 'string', required: false }
        }
      },
      {
        name: 'search_code',
        description: 'Search for code across GitHub repositories',
        parameters: {
          query: { type: 'string', required: true },
          sort: { type: 'string', required: false },
          order: { type: 'string', required: false }
        }
      },
      {
        name: 'get_file_content',
        description: 'Get the content of a file from a repository',
        parameters: {
          owner: { type: 'string', required: true },
          repo: { type: 'string', required: true },
          path: { type: 'string', required: true }
        }
      }
    ]
  },
  figma: {
    serviceId: 'figma',
    displayName: 'Figma',
    description: 'Connect to Figma for design file access, asset export, and team collaboration',
    iconUrl: '/icons/figma.svg',
    scopes: ['file:read', 'file:write', 'team:read'],
    tools: [
      {
        name: 'get_file_content',
        description: 'Get the content and structure of a Figma file',
        parameters: {
          file_key: { type: 'string', required: true },
          version: { type: 'string', required: false }
        }
      },
      {
        name: 'export_assets',
        description: 'Export assets from a Figma file',
        parameters: {
          file_key: { type: 'string', required: true },
          ids: { type: 'array', required: true },
          format: { type: 'string', required: true }
        }
      },
      {
        name: 'create_component',
        description: 'Create a component from a node in Figma',
        parameters: {
          file_key: { type: 'string', required: true },
          node_id: { type: 'string', required: true },
          name: { type: 'string', required: true }
        }
      },
      {
        name: 'get_team_projects',
        description: 'Get projects from a Figma team',
        parameters: {
          team_id: { type: 'string', required: true }
        }
      }
    ]
  },
  atlassian: {
    serviceId: 'atlassian',
    displayName: 'Atlassian',
    description: 'Connect to Atlassian for Jira issue tracking, Confluence documentation, and Bitbucket code collaboration',
    iconUrl: '/icons/atlassian.svg',
    scopes: [
      'read:jira-work',
      'write:jira-work',
      'read:jira-user',
      'read:confluence-content.all',
      'write:confluence-content',
      'read:confluence-space.summary',
      'repository:read',
      'repository:write',
      'pullrequest:read',
      'pullrequest:write',
      'issue:read',
      'issue:write'
    ],
    tools: [
      {
        name: 'create_jira_issue',
        description: 'Create a new Jira issue',
        parameters: {
          cloudId: { type: 'string', required: true },
          projectKey: { type: 'string', required: true },
          summary: { type: 'string', required: true },
          description: { type: 'string', required: false },
          issueType: { type: 'string', required: true }
        }
      },
      {
        name: 'search_jira_issues',
        description: 'Search Jira issues using JQL',
        parameters: {
          cloudId: { type: 'string', required: true },
          jql: { type: 'string', required: true },
          maxResults: { type: 'number', required: false }
        }
      },
      {
        name: 'create_confluence_page',
        description: 'Create a new Confluence page',
        parameters: {
          cloudId: { type: 'string', required: true },
          spaceKey: { type: 'string', required: true },
          title: { type: 'string', required: true },
          body: { type: 'string', required: true }
        }
      },
      {
        name: 'search_confluence_content',
        description: 'Search Confluence content using CQL',
        parameters: {
          cloudId: { type: 'string', required: true },
          cql: { type: 'string', required: true },
          limit: { type: 'number', required: false }
        }
      },
      {
        name: 'create_bitbucket_repository',
        description: 'Create a new Bitbucket repository',
        parameters: {
          workspace: { type: 'string', required: true },
          name: { type: 'string', required: true },
          description: { type: 'string', required: false },
          is_private: { type: 'boolean', required: false }
        }
      },
      {
        name: 'search_bitbucket_code',
        description: 'Search code in Bitbucket repositories',
        parameters: {
          workspace: { type: 'string', required: true },
          repo_slug: { type: 'string', required: true },
          search_query: { type: 'string', required: true }
        }
      },
      {
        name: 'list_accessible_resources',
        description: 'List accessible Atlassian resources',
        parameters: {}
      }
    ]
  }
};

// Note: Window API types are defined in vite-env.d.ts to avoid conflicts

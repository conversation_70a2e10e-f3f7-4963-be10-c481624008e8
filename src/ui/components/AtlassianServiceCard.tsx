import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Switch } from './ui/switch';
import { Alert, AlertDescription } from './ui/alert';
import { Separator } from './ui/separator';
import { MCPServiceStatus, AtlassianToolParams } from '../types/mcp-types';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Settings, 
  ExternalLink,
  FileText,
  GitBranch,
  Search,
  Plus,
  Users,
  BookOpen,
  Trello
} from 'lucide-react';

interface AtlassianServiceCardProps {
  service: MCPServiceStatus;
  onAuth: () => void;
  onToggle: (enabled: boolean) => void;
}

export function AtlassianServiceCard({ service, onAuth, onToggle }: AtlassianServiceCardProps) {
  const [executing, setExecuting] = useState<string | null>(null);
  const [toolResults, setToolResults] = useState<Record<string, any>>({});

  const getStatusIcon = () => {
    if (!service.enabled) return <AlertCircle className="h-4 w-4 text-gray-400" />;
    if (!service.authenticated) return <Clock className="h-4 w-4 text-yellow-500" />;
    if (!service.healthy) return <AlertCircle className="h-4 w-4 text-red-500" />;
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (!service.enabled) return 'Disabled';
    if (!service.authenticated) return 'Authentication Required';
    if (!service.healthy) return 'Unhealthy';
    return 'Connected';
  };

  const getStatusVariant = () => {
    if (!service.enabled) return 'secondary';
    if (!service.authenticated) return 'outline';
    if (!service.healthy) return 'destructive';
    return 'default';
  };

  const executeTool = async (toolName: string, params: any) => {
    setExecuting(toolName);
    try {
      const result = await window.api.mcpExecuteTool(service.serviceId, toolName, params);
      setToolResults(prev => ({ ...prev, [toolName]: result }));
    } catch (error) {
      console.error(`Failed to execute ${toolName}:`, error);
      setToolResults(prev => ({ 
        ...prev, 
        [toolName]: { 
          success: false, 
          error: error instanceof Error ? error.message : String(error) 
        } 
      }));
    } finally {
      setExecuting(null);
    }
  };

  const getServiceIcon = () => {
    return (
      <div className="p-2 bg-blue-100 rounded-lg">
        <Trello className="h-6 w-6 text-blue-600" />
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getServiceIcon()}
            <div>
              <CardTitle className="flex items-center gap-2">
                Atlassian
                {getStatusIcon()}
              </CardTitle>
              <CardDescription>
                Jira, Confluence, and Bitbucket integration
              </CardDescription>
            </div>
          </div>
          <Switch
            checked={service.enabled}
            onCheckedChange={onToggle}
          />
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status Section */}
        <div className="flex items-center justify-between">
          <Badge variant={getStatusVariant()}>
            {getStatusText()}
          </Badge>
          {service.authStatus.expiresAt && (
            <span className="text-xs text-muted-foreground">
              Expires: {new Date(service.authStatus.expiresAt).toLocaleDateString()}
            </span>
          )}
        </div>

        {/* Authentication Section */}
        {service.enabled && !service.authenticated && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Connect your Atlassian account to access Jira, Confluence, and Bitbucket.
            </AlertDescription>
          </Alert>
        )}

        {service.enabled && (
          <div className="space-y-3">
            {!service.authenticated ? (
              <Button onClick={onAuth} className="w-full">
                <Trello className="w-4 h-4 mr-2" />
                Connect to Atlassian
              </Button>
            ) : (
              <div className="space-y-3">
                {/* Scopes Display */}
                {service.authStatus.scopes && (
                  <div>
                    <div className="text-sm font-medium mb-2">Granted Permissions:</div>
                    <div className="flex flex-wrap gap-1">
                      {service.authStatus.scopes.map((scope) => (
                        <Badge key={scope} variant="outline" className="text-xs">
                          {scope}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* Service Categories */}
                <div className="space-y-3">
                  <div className="text-sm font-medium">Available Services:</div>
                  
                  {/* Jira Tools */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-blue-600">
                      <FileText className="h-4 w-4" />
                      Jira (Issue Tracking)
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => executeTool('create_jira_issue', {
                          cloudId: 'demo',
                          projectKey: 'DEMO',
                          summary: 'Test Issue',
                          issueType: 'Task'
                        })}
                        disabled={executing === 'create_jira_issue'}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Create Issue
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => executeTool('search_jira_issues', {
                          cloudId: 'demo',
                          jql: 'project = DEMO'
                        })}
                        disabled={executing === 'search_jira_issues'}
                      >
                        <Search className="w-3 h-3 mr-1" />
                        Search Issues
                      </Button>
                    </div>
                  </div>

                  {/* Confluence Tools */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-green-600">
                      <BookOpen className="h-4 w-4" />
                      Confluence (Documentation)
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => executeTool('create_confluence_page', {
                          cloudId: 'demo',
                          spaceKey: 'DEMO',
                          title: 'Test Page',
                          body: '<p>Test content</p>'
                        })}
                        disabled={executing === 'create_confluence_page'}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Create Page
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => executeTool('search_confluence_content', {
                          cloudId: 'demo',
                          cql: 'type=page'
                        })}
                        disabled={executing === 'search_confluence_content'}
                      >
                        <Search className="w-3 h-3 mr-1" />
                        Search Content
                      </Button>
                    </div>
                  </div>

                  {/* Bitbucket Tools */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-purple-600">
                      <GitBranch className="h-4 w-4" />
                      Bitbucket (Code Repository)
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => executeTool('create_bitbucket_repository', {
                          workspace: 'demo',
                          name: 'test-repo',
                          description: 'Test repository'
                        })}
                        disabled={executing === 'create_bitbucket_repository'}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Create Repo
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => executeTool('search_bitbucket_code', {
                          workspace: 'demo',
                          repo_slug: 'test-repo',
                          search_query: 'function'
                        })}
                        disabled={executing === 'search_bitbucket_code'}
                      >
                        <Search className="w-3 h-3 mr-1" />
                        Search Code
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Tool Results */}
                {Object.keys(toolResults).length > 0 && (
                  <div className="space-y-2">
                    <Separator />
                    <div className="text-sm font-medium">Recent Results:</div>
                    {Object.entries(toolResults).map(([tool, result]) => (
                      <div key={tool} className="text-xs p-2 bg-muted rounded">
                        <div className="font-medium">{tool}:</div>
                        <div className={result.success ? "text-green-600" : "text-red-600"}>
                          {result.success ? "✓ Success" : `✗ ${result.error}`}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Settings */}
                <div className="flex justify-between items-center pt-2">
                  <Button variant="ghost" size="sm">
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </Button>
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Atlassian
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

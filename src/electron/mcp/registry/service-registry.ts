import { RemoteService } from '../clients/remote-service-client.js';
import { GitHubService } from '../clients/github-service.js';
import { FigmaService } from '../clients/figma-service.js';
import { FigmaLocalService } from '../clients/figma-local-service.js';
import { AtlassianService } from '../clients/atlassian-service.js';
import { LocalServiceClient } from '../clients/local-service-client.js';
import { MCPConfigurationManager } from '../config/mcp-configuration-manager.js';
import { OAuthCallbackRouter } from '../auth/oauth-callback-router.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';

// Base service interface that both remote and local services implement
export interface BaseService {
  readonly serviceId: string;
  readonly displayName: string;
  initialize(): Promise<void>;
  executeTool(toolName: string, parameters: any): Promise<any>;
  getAuthStatus(): Promise<any>;
  getAvailableTools(): string[];
  isHealthy(): boolean;
  close(): Promise<void>;
}

export interface ServiceRegistryStats {
  totalServices: number;
  enabledServices: number;
  authenticatedServices: number;
  healthyServices: number;
  services: Array<{
    serviceId: string;
    displayName: string;
    enabled: boolean;
    authenticated: boolean;
    healthy: boolean;
    lastError?: string;
  }>;
}

export class ServiceRegistry {
  private services = new Map<string, BaseService>();
  private configManager: MCPConfigurationManager;
  private callbackRouter: OAuthCallbackRouter;
  private static instance: ServiceRegistry;

  constructor(configManager: MCPConfigurationManager) {
    this.configManager = configManager;
    this.callbackRouter = new OAuthCallbackRouter();
  }

  static getInstance(configManager?: MCPConfigurationManager): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      if (!configManager) {
        throw new Error('ConfigurationManager required for first ServiceRegistry instantiation');
      }
      ServiceRegistry.instance = new ServiceRegistry(configManager);
    }
    return ServiceRegistry.instance;
  }

  async initializeFromConfiguration(): Promise<void> {
    console.log('Initializing services from configuration...');

    try {
      const enabledServers = this.configManager.getEnabledServers();
      console.log(`Found ${enabledServers.length} enabled servers: ${enabledServers.join(', ')}`);

      for (const serverId of enabledServers) {
        const config = this.configManager.getServerConfiguration(serverId);
        if (config) {
          await this.createAndRegisterService(serverId, config);
        }
      }

      console.log(`✅ Service registry initialized with ${this.services.size} services`);
    } catch (error) {
      console.error('❌ Failed to initialize service registry:', error);
      throw error;
    }
  }

  async createAndRegisterService(serverId: string, config: ServerConfiguration): Promise<void> {
    console.log(`Creating and registering service: ${serverId}`);

    try {
      // Create service instance based on service ID
      const service = this.createServiceInstance(serverId, config);

      // Set discovered OAuth metadata if available and discovery endpoint is configured (only for remote services)
      if (this.isRemoteService(service) && config.endpoints?.discovery) {
        const discoveryClient = this.configManager.getDiscoveryClient();
        try {
          const metadata = discoveryClient.getCachedMetadata(config.endpoints.discovery);
          if (metadata && 'setDiscoveredMetadata' in service) {
            (service as any).setDiscoveredMetadata(metadata);
          }
        } catch (error) {
          console.warn(`Could not set discovered metadata for ${serverId}:`, error);
        }
      } else if (this.isRemoteService(service)) {
        console.log(`Using static OAuth configuration for ${serverId} (no discovery endpoint configured)`);
      }

      // Register service
      this.registerService(service);

      // Check if service is already authenticated and initialize if so
      try {
        const authStatus = await service.getAuthStatus();
        if (authStatus.authenticated) {
          console.log(`🔄 Service ${serverId} is authenticated, initializing...`);
          await service.initialize();
          console.log(`✅ Service ${serverId} initialized successfully`);
        } else {
          console.log(`ℹ️ Service ${serverId} not authenticated, skipping initialization`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to initialize authenticated service ${serverId}:`, error);
        // Continue without failing the registration
      }

      console.log(`✅ Service ${serverId} created and registered successfully`);
    } catch (error) {
      console.error(`❌ Failed to create and register service ${serverId}:`, error);
      throw error;
    }
  }

  registerService(service: BaseService): void {
    this.services.set(service.serviceId, service);

    // Only register OAuth callback handler for remote services
    if (this.isRemoteService(service)) {
      this.callbackRouter.registerHandler(service as RemoteService);
    }

    console.log(`Registered service: ${service.serviceId} (${service.displayName})`);
  }

  unregisterService(serviceId: string): void {
    const service = this.services.get(serviceId);
    if (service) {
      this.services.delete(serviceId);

      // Only unregister OAuth callback handler for remote services
      if (this.isRemoteService(service)) {
        this.callbackRouter.unregisterHandler(serviceId);
      }

      console.log(`Unregistered service: ${serviceId}`);
    }
  }

  getService(serviceId: string): BaseService | undefined {
    return this.services.get(serviceId);
  }

  getAllServices(): BaseService[] {
    return Array.from(this.services.values());
  }

  getEnabledServices(): BaseService[] {
    return this.getAllServices().filter(service => {
      const config = this.configManager.getServerConfiguration(service.serviceId);
      return config?.enabled ?? false;
    });
  }

  async getAuthenticatedServices(): Promise<BaseService[]> {
    const services = this.getAllServices();
    const authenticatedServices: BaseService[] = [];

    for (const service of services) {
      try {
        const authStatus = await service.getAuthStatus();
        if (authStatus.authenticated) {
          authenticatedServices.push(service);
        }
      } catch (error) {
        console.warn(`Failed to check auth status for ${service.serviceId}:`, error);
      }
    }

    return authenticatedServices;
  }

  getHealthyServices(): BaseService[] {
    return this.getAllServices().filter(service => service.isHealthy());
  }

  async initializeAuthenticatedServices(): Promise<void> {
    console.log('🔄 Initializing all authenticated services...');
    const services = this.getAllServices();

    for (const service of services) {
      try {
        const authStatus = await service.getAuthStatus();
        if (authStatus.authenticated && !service.isHealthy()) {
          console.log(`🔄 Initializing authenticated service: ${service.serviceId}`);
          await service.initialize();
          console.log(`✅ Service ${service.serviceId} initialized successfully`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to initialize service ${service.serviceId}:`, error);
      }
    }

    console.log('✅ Authenticated services initialization complete');
  }

  async authenticateService(serviceId: string): Promise<string> {
    const service = this.getService(serviceId);
    if (!service) {
      throw new Error(`Service not found: ${serviceId}`);
    }

    if (!this.isRemoteService(service)) {
      throw new Error(`Service ${serviceId} does not support OAuth authentication`);
    }

    console.log(`Starting authentication for service: ${serviceId}`);
    return await (service as RemoteService).authenticate();
  }

  async handleOAuthCallback(url: string): Promise<void> {
    console.log(`Handling OAuth callback: ${url}`);
    const result = await this.callbackRouter.routeCallback(url);
    
    if (!result.success) {
      throw new Error(`OAuth callback failed: ${result.error}`);
    }

    console.log(`✅ OAuth callback handled successfully for ${result.serviceId}`);
  }

  async executeToolOnService(serviceId: string, toolName: string, parameters: any): Promise<any> {
    const service = this.getService(serviceId);
    if (!service) {
      throw new Error(`Service not found: ${serviceId}`);
    }

    const authStatus = await service.getAuthStatus();
    if (!authStatus.authenticated) {
      throw new Error(`Service ${serviceId} is not authenticated`);
    }

    if (!service.isHealthy()) {
      throw new Error(`Service ${serviceId} is not healthy`);
    }

    return await service.executeTool(toolName, parameters);
  }

  async enableService(serviceId: string): Promise<void> {
    await this.configManager.enableServer(serviceId);
    
    // If service is not registered, create and register it
    if (!this.services.has(serviceId)) {
      const config = this.configManager.getServerConfiguration(serviceId);
      if (config) {
        await this.createAndRegisterService(serviceId, config);
      }
    }

    console.log(`✅ Service ${serviceId} enabled`);
  }

  async disableService(serviceId: string): Promise<void> {
    await this.configManager.disableServer(serviceId);
    
    // Close and unregister the service
    const service = this.getService(serviceId);
    if (service) {
      await service.close();
      this.unregisterService(serviceId);
    }

    console.log(`✅ Service ${serviceId} disabled`);
  }

  async getServiceStats(): Promise<ServiceRegistryStats> {
    const allServices = this.getAllServices();
    const enabledServices = this.getEnabledServices();
    const authenticatedServices = await this.getAuthenticatedServices();
    const healthyServices = this.getHealthyServices();

    const serviceDetails = await Promise.all(
      allServices.map(async (service) => {
        const config = this.configManager.getServerConfiguration(service.serviceId);
        const authStatus = await service.getAuthStatus().catch(() => ({ authenticated: false, error: 'Failed to get auth status' }));

        return {
          serviceId: service.serviceId,
          displayName: service.displayName,
          enabled: config?.enabled ?? false,
          authenticated: authStatus.authenticated,
          healthy: service.isHealthy(),
          lastError: authStatus.error
        };
      })
    );

    return {
      totalServices: allServices.length,
      enabledServices: enabledServices.length,
      authenticatedServices: authenticatedServices.length,
      healthyServices: healthyServices.length,
      services: serviceDetails
    };
  }

  async closeAllServices(): Promise<void> {
    console.log('Closing all services...');
    
    const closePromises = this.getAllServices().map(async (service) => {
      try {
        await service.close();
      } catch (error) {
        console.error(`Failed to close service ${service.serviceId}:`, error);
      }
    });

    await Promise.all(closePromises);
    this.services.clear();
    
    console.log('✅ All services closed');
  }

  private createServiceInstance(serviceId: string, config: ServerConfiguration): BaseService {
    switch (serviceId) {
      case 'github':
        return new GitHubService(config);
      case 'figma':
        // Choose between remote and local based on transport type
        if (config.transport === 'stdio') {
          return new FigmaLocalService(config);
        } else {
          return new FigmaService(config);
        }
      case 'atlassian':
        return new AtlassianService(config);
      default:
        throw new Error(`Unknown service type: ${serviceId}`);
    }
  }

  private isRemoteService(service: BaseService): service is RemoteService {
    return 'callbackPath' in service && 'endpoints' in service && 'authenticate' in service;
  }

  getCallbackRouter(): OAuthCallbackRouter {
    return this.callbackRouter;
  }
}

import { EventEmitter } from 'events';
import { RemoteServiceClient, ToolResult } from './remote-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';
import { AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';
import { AtlassianOAuthCallbackProcessor } from '../auth/atlassian-oauth-callback-processor.js';

export interface AtlassianResource {
  id: string;
  name: string;
  url: string;
  scopes: string[];
  avatarUrl: string;
}

export interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description?: string;
  status: string;
  assignee?: string;
  reporter: string;
  created: string;
  updated: string;
  priority: string;
  issueType: string;
}

export interface ConfluencePage {
  id: string;
  title: string;
  type: string;
  status: string;
  space: {
    id: string;
    key: string;
    name: string;
  };
  version: {
    number: number;
    when: string;
    by: string;
  };
  body?: {
    storage: {
      value: string;
      representation: string;
    };
  };
}

export interface BitbucketRepository {
  uuid: string;
  name: string;
  full_name: string;
  description?: string;
  is_private: boolean;
  created_on: string;
  updated_on: string;
  language?: string;
  size: number;
}

export class AtlassianService extends RemoteServiceClient {
  private accessibleResources: AtlassianResource[] = [];
  private eventEmitter: EventEmitter;
  private oauthCallbackProcessor: AtlassianOAuthCallbackProcessor;

  constructor(config: ServerConfiguration) {
    super('atlassian', 'Atlassian', config);
    this.eventEmitter = new EventEmitter();
    this.oauthCallbackProcessor = new AtlassianOAuthCallbackProcessor();
  }

  // EventEmitter delegation methods
  emit(event: string | symbol, ...args: any[]): boolean {
    return this.eventEmitter.emit(event, ...args);
  }

  on(event: string | symbol, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  off(event: string | symbol, listener: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  // OAuth Callback Handler implementation
  async handleCallback(params: URLSearchParams): Promise<void> {
    return await this.oauthCallbackProcessor.handleCallback(params);
  }

  setDiscoveredMetadata(metadata: AuthorizationServerMetadata): void {
    this.oauthService.setDiscoveredMetadata(metadata);
    console.log('Atlassian OAuth metadata updated from discovery');
  }

  async initialize(): Promise<void> {
    // Skip the parent initialize to avoid trying to connect to non-existent MCP endpoints
    console.log('Initializing Atlassian service with direct API access...');

    // Initialize OAuth service directly
    try {
      const authStatus = await this.oauthCallbackProcessor.getAuthStatus();
      if (authStatus.authenticated) {
        await this.loadAccessibleResources();
        console.log('✅ Atlassian service initialized successfully');
      } else {
        console.log('⚠️ Atlassian service initialized but not authenticated');
      }
    } catch (error) {
      console.error('❌ Failed to initialize Atlassian service:', error);
    }
  }

  protected async performServiceSpecificInitialization(): Promise<void> {
    // Load accessible resources after authentication
    const authStatus = await this.oauthCallbackProcessor.getAuthStatus();
    if (authStatus.authenticated) {
      await this.loadAccessibleResources();
    }
  }

  // Override transport methods to prevent connection attempts to non-existent MCP endpoints
  protected async connectTransport(): Promise<void> {
    console.log('Atlassian service using direct API calls - no transport connection needed');
    // Do nothing - we make direct API calls
  }

  protected async disconnectTransport(): Promise<void> {
    console.log('Atlassian service disconnecting - no transport to disconnect');
    // Do nothing - no transport to disconnect
  }

  // Override health check to use authentication status instead of transport connection
  isHealthy(): boolean {
    // For direct API access, we're healthy if we can potentially make API calls
    return true; // We'll check authentication status separately
  }

  async getAuthStatus(): Promise<{
    authenticated: boolean;
    expiresAt?: Date;
    scopes?: string[];
    error?: string;
  }> {
    try {
      return await this.oauthCallbackProcessor.getAuthStatus();
    } catch (error) {
      return {
        authenticated: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  protected handleIncomingMessage(message: any): void {
    console.log('Atlassian incoming message:', message);
    // Handle real-time notifications from Atlassian services
    if (message.type && message.payload) {
      this.handleSSEEvent(new MessageEvent('message', { data: JSON.stringify(message) }));
    }
  }

  protected handleTransportError(error: Error): void {
    console.error('Atlassian transport error:', error);
    // Emit error event for UI handling
    this.emit('transport:error', error);
  }

  protected handleTransportClose(): void {
    console.log('Atlassian transport connection closed');
    // Emit close event for UI handling
    this.emit('transport:close');
  }

  async loadAccessibleResources(): Promise<void> {
    try {
      const result = await this.executeTool('list_accessible_resources', {});
      if (result.content && result.content.length > 0) {
        const textContent = result.content.find(c => c.type === 'text');
        if (textContent && textContent.text) {
          const data = JSON.parse(textContent.text);
          this.accessibleResources = data as AtlassianResource[];
          console.log(`Loaded ${this.accessibleResources.length} accessible Atlassian resources`);
        }
      }
    } catch (error) {
      console.error('Failed to load accessible Atlassian resources:', error);
    }
  }

  getAccessibleResources(): AtlassianResource[] {
    return [...this.accessibleResources];
  }

  // Resource Discovery
  async listAccessibleResources(): Promise<ToolResult> {
    try {
      const accessToken = await this.oauthCallbackProcessor.getAccessToken();
      if (!accessToken) {
        throw new Error('No access token available for Atlassian');
      }

      const response = await fetch('https://api.atlassian.com/oauth/token/accessible-resources', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Atlassian API error: ${response.status} ${errorText}`);
      }

      const resources = await response.json();
      this.accessibleResources = resources;

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(resources, null, 2)
        }],
        isError: false
      };
    } catch (error) {
      console.error('Failed to list accessible resources:', error);
      return {
        content: [{
          type: 'text',
          text: `Error: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  // Jira Operations
  async createJiraIssue(params: {
    cloudId: string;
    projectKey: string;
    summary: string;
    description?: string;
    issueType: string;
    priority?: string;
    assignee?: string;
    labels?: string[];
    components?: string[];
  }): Promise<ToolResult> {
    try {
      const accessToken = await this.oauthCallbackProcessor.getAccessToken();
      if (!accessToken) {
        throw new Error('No access token available for Atlassian');
      }

      const response = await fetch(`https://api.atlassian.com/ex/jira/${params.cloudId}/rest/api/3/issue`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fields: {
            project: { key: params.projectKey },
            summary: params.summary,
            description: params.description ? {
              type: 'doc',
              version: 1,
              content: [{
                type: 'paragraph',
                content: [{ type: 'text', text: params.description }]
              }]
            } : undefined,
            issuetype: { name: params.issueType },
            priority: params.priority ? { name: params.priority } : undefined,
            assignee: params.assignee ? { accountId: params.assignee } : undefined,
            labels: params.labels || [],
            components: params.components?.map(name => ({ name })) || []
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Jira API error: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }],
        isError: false
      };
    } catch (error) {
      console.error('Failed to create Jira issue:', error);
      return {
        content: [{
          type: 'text',
          text: `Error: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  async updateJiraIssue(params: {
    cloudId: string;
    issueIdOrKey: string;
    summary?: string;
    description?: string;
    assignee?: string;
    priority?: string;
    status?: string;
    labels?: string[];
    components?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('update_jira_issue', params);
  }

  async searchJiraIssues(params: {
    cloudId: string;
    jql: string;
    startAt?: number;
    maxResults?: number;
    fields?: string[];
    expand?: string[];
  }): Promise<ToolResult> {
    try {
      const accessToken = await this.oauthCallbackProcessor.getAccessToken();
      if (!accessToken) {
        throw new Error('No access token available for Atlassian');
      }

      const searchParams = new URLSearchParams({
        jql: params.jql,
        startAt: (params.startAt || 0).toString(),
        maxResults: (params.maxResults || 50).toString(),
        fields: params.fields?.join(',') || 'summary,status,assignee,created',
        expand: params.expand?.join(',') || ''
      });

      const response = await fetch(`https://api.atlassian.com/ex/jira/${params.cloudId}/rest/api/3/search?${searchParams}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Jira API error: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }],
        isError: false
      };
    } catch (error) {
      console.error('Failed to search Jira issues:', error);
      return {
        content: [{
          type: 'text',
          text: `Error: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  async getJiraIssue(params: {
    cloudId: string;
    issueIdOrKey: string;
    fields?: string[];
    expand?: string[];
    properties?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('get_jira_issue', params);
  }

  // Confluence Operations
  async createConfluencePage(params: {
    cloudId: string;
    spaceKey: string;
    title: string;
    body: string;
    parentId?: string;
    type?: 'page' | 'blogpost';
    representation?: 'storage' | 'wiki' | 'view';
  }): Promise<ToolResult> {
    try {
      const accessToken = await this.oauthCallbackProcessor.getAccessToken();
      if (!accessToken) {
        throw new Error('No access token available for Atlassian');
      }

      const response = await fetch(`https://api.atlassian.com/ex/confluence/${params.cloudId}/rest/api/content`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: params.type || 'page',
          title: params.title,
          space: { key: params.spaceKey },
          body: {
            [params.representation || 'storage']: {
              value: params.body,
              representation: params.representation || 'storage'
            }
          },
          ancestors: params.parentId ? [{ id: params.parentId }] : undefined
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Confluence API error: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }],
        isError: false
      };
    } catch (error) {
      console.error('Failed to create Confluence page:', error);
      return {
        content: [{
          type: 'text',
          text: `Error: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  async updateConfluencePage(params: {
    cloudId: string;
    pageId: string;
    title?: string;
    body?: string;
    version: number;
    representation?: 'storage' | 'wiki' | 'view';
  }): Promise<ToolResult> {
    return await this.executeTool('update_confluence_page', params);
  }

  async searchConfluenceContent(params: {
    cloudId: string;
    cql: string;
    start?: number;
    limit?: number;
    includeArchivedSpaces?: boolean;
    expand?: string[];
  }): Promise<ToolResult> {
    try {
      const accessToken = await this.oauthCallbackProcessor.getAccessToken();
      if (!accessToken) {
        throw new Error('No access token available for Atlassian');
      }

      const searchParams = new URLSearchParams({
        cql: params.cql,
        start: (params.start || 0).toString(),
        limit: (params.limit || 25).toString(),
        includeArchivedSpaces: (params.includeArchivedSpaces || false).toString(),
        expand: params.expand?.join(',') || 'body.view,version'
      });

      const response = await fetch(`https://api.atlassian.com/ex/confluence/${params.cloudId}/rest/api/content/search?${searchParams}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Confluence API error: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }],
        isError: false
      };
    } catch (error) {
      console.error('Failed to search Confluence content:', error);
      return {
        content: [{
          type: 'text',
          text: `Error: ${error instanceof Error ? error.message : String(error)}`
        }],
        isError: true
      };
    }
  }

  async getConfluencePage(params: {
    cloudId: string;
    pageId: string;
    expand?: string[];
    version?: number;
  }): Promise<ToolResult> {
    return await this.executeTool('get_confluence_page', params);
  }

  // Bitbucket Operations
  async createBitbucketRepository(params: {
    workspace: string;
    name: string;
    description?: string;
    is_private?: boolean;
    language?: string;
    has_issues?: boolean;
    has_wiki?: boolean;
    fork_policy?: 'allow_forks' | 'no_public_forks' | 'no_forks';
    project?: {
      key: string;
    };
  }): Promise<ToolResult> {
    return await this.executeTool('create_bitbucket_repository', params);
  }

  async createBitbucketPullRequest(params: {
    workspace: string;
    repo_slug: string;
    title: string;
    description?: string;
    source: {
      branch: {
        name: string;
      };
    };
    destination: {
      branch: {
        name: string;
      };
    };
    reviewers?: Array<{
      uuid: string;
    }>;
    close_source_branch?: boolean;
  }): Promise<ToolResult> {
    return await this.executeTool('create_bitbucket_pull_request', params);
  }

  async searchBitbucketCode(params: {
    workspace: string;
    repo_slug: string;
    search_query: string;
    page?: number;
    pagelen?: number;
  }): Promise<ToolResult> {
    return await this.executeTool('search_bitbucket_code', params);
  }

  async getBitbucketFileContent(params: {
    workspace: string;
    repo_slug: string;
    commit: string;
    path: string;
    format?: 'raw' | 'rendered';
  }): Promise<ToolResult> {
    return await this.executeTool('get_bitbucket_file_content', params);
  }

  // Utility Methods
  async getJiraProjects(cloudId: string): Promise<ToolResult> {
    return await this.searchJiraIssues({
      cloudId,
      jql: 'project is not EMPTY',
      maxResults: 0,
      fields: ['project']
    });
  }

  async getConfluenceSpaces(cloudId: string): Promise<ToolResult> {
    return await this.searchConfluenceContent({
      cloudId,
      cql: 'type=space',
      limit: 50,
      expand: ['metadata.labels', 'icon', 'description.plain', 'homepage']
    });
  }

  async getBitbucketWorkspaces(): Promise<ToolResult> {
    // This would typically be handled through the accessible resources
    const resources = this.getAccessibleResources();
    const bitbucketResources = resources.filter(r =>
      r.scopes.some(scope => scope.startsWith('repository'))
    );

    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: true,
          data: bitbucketResources,
          metadata: {
            total: bitbucketResources.length,
            service: 'bitbucket'
          }
        })
      }],
      isError: false
    };
  }

  // Event Handlers for SSE
  protected handleSSEEvent(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case 'jira:issue:updated':
          this.handleJiraIssueUpdate(data.payload);
          break;
        case 'confluence:page:updated':
          this.handleConfluencePageUpdate(data.payload);
          break;
        case 'bitbucket:repository:push':
          this.handleBitbucketPush(data.payload);
          break;
        case 'bitbucket:pullrequest:updated':
          this.handleBitbucketPullRequestUpdate(data.payload);
          break;
        default:
          console.log('Unknown Atlassian SSE event type:', data.type);
      }
    } catch (error) {
      console.error('Error processing Atlassian SSE event:', error);
    }
  }

  private handleJiraIssueUpdate(payload: any): void {
    console.log('Jira issue updated:', payload);
    // Emit event for UI updates
    this.emit('jira:issue:updated', payload);
  }

  private handleConfluencePageUpdate(payload: any): void {
    console.log('Confluence page updated:', payload);
    // Emit event for UI updates
    this.emit('confluence:page:updated', payload);
  }

  private handleBitbucketPush(payload: any): void {
    console.log('Bitbucket repository push:', payload);
    // Emit event for UI updates
    this.emit('bitbucket:repository:push', payload);
  }

  private handleBitbucketPullRequestUpdate(payload: any): void {
    console.log('Bitbucket pull request updated:', payload);
    // Emit event for UI updates
    this.emit('bitbucket:pullrequest:updated', payload);
  }
}

import { RemoteServiceClient, ToolResult } from './remote-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';
import { AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';

export interface AtlassianResource {
  id: string;
  name: string;
  url: string;
  scopes: string[];
  avatarUrl: string;
}

export interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description?: string;
  status: string;
  assignee?: string;
  reporter: string;
  created: string;
  updated: string;
  priority: string;
  issueType: string;
}

export interface ConfluencePage {
  id: string;
  title: string;
  type: string;
  status: string;
  space: {
    id: string;
    key: string;
    name: string;
  };
  version: {
    number: number;
    when: string;
    by: string;
  };
  body?: {
    storage: {
      value: string;
      representation: string;
    };
  };
}

export interface BitbucketRepository {
  uuid: string;
  name: string;
  full_name: string;
  description?: string;
  is_private: boolean;
  created_on: string;
  updated_on: string;
  language?: string;
  size: number;
}

export class AtlassianService extends RemoteServiceClient {
  private accessibleResources: AtlassianResource[] = [];

  constructor(config: ServerConfiguration) {
    super('atlassian', 'Atlassian', config);
  }

  setDiscoveredMetadata(metadata: AuthorizationServerMetadata): void {
    this.oauthService.setDiscoveredMetadata(metadata);
    console.log('Atlassian OAuth metadata updated from discovery');
  }

  async initialize(): Promise<void> {
    await super.initialize();
    
    // Load accessible resources after authentication
    if (await this.oauthService.isAuthenticated()) {
      await this.loadAccessibleResources();
    }
  }

  async loadAccessibleResources(): Promise<void> {
    try {
      const result = await this.executeTool('list_accessible_resources', {});
      if (result.success && result.data) {
        this.accessibleResources = result.data as AtlassianResource[];
        console.log(`Loaded ${this.accessibleResources.length} accessible Atlassian resources`);
      }
    } catch (error) {
      console.error('Failed to load accessible Atlassian resources:', error);
    }
  }

  getAccessibleResources(): AtlassianResource[] {
    return [...this.accessibleResources];
  }

  // Resource Discovery
  async listAccessibleResources(): Promise<ToolResult> {
    return await this.executeTool('list_accessible_resources', {});
  }

  // Jira Operations
  async createJiraIssue(params: {
    cloudId: string;
    projectKey: string;
    summary: string;
    description?: string;
    issueType: string;
    priority?: string;
    assignee?: string;
    labels?: string[];
    components?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('create_jira_issue', params);
  }

  async updateJiraIssue(params: {
    cloudId: string;
    issueIdOrKey: string;
    summary?: string;
    description?: string;
    assignee?: string;
    priority?: string;
    status?: string;
    labels?: string[];
    components?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('update_jira_issue', params);
  }

  async searchJiraIssues(params: {
    cloudId: string;
    jql: string;
    startAt?: number;
    maxResults?: number;
    fields?: string[];
    expand?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('search_jira_issues', params);
  }

  async getJiraIssue(params: {
    cloudId: string;
    issueIdOrKey: string;
    fields?: string[];
    expand?: string[];
    properties?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('get_jira_issue', params);
  }

  // Confluence Operations
  async createConfluencePage(params: {
    cloudId: string;
    spaceKey: string;
    title: string;
    body: string;
    parentId?: string;
    type?: 'page' | 'blogpost';
    representation?: 'storage' | 'wiki' | 'view';
  }): Promise<ToolResult> {
    return await this.executeTool('create_confluence_page', params);
  }

  async updateConfluencePage(params: {
    cloudId: string;
    pageId: string;
    title?: string;
    body?: string;
    version: number;
    representation?: 'storage' | 'wiki' | 'view';
  }): Promise<ToolResult> {
    return await this.executeTool('update_confluence_page', params);
  }

  async searchConfluenceContent(params: {
    cloudId: string;
    cql: string;
    start?: number;
    limit?: number;
    includeArchivedSpaces?: boolean;
    expand?: string[];
  }): Promise<ToolResult> {
    return await this.executeTool('search_confluence_content', params);
  }

  async getConfluencePage(params: {
    cloudId: string;
    pageId: string;
    expand?: string[];
    version?: number;
  }): Promise<ToolResult> {
    return await this.executeTool('get_confluence_page', params);
  }

  // Bitbucket Operations
  async createBitbucketRepository(params: {
    workspace: string;
    name: string;
    description?: string;
    is_private?: boolean;
    language?: string;
    has_issues?: boolean;
    has_wiki?: boolean;
    fork_policy?: 'allow_forks' | 'no_public_forks' | 'no_forks';
    project?: {
      key: string;
    };
  }): Promise<ToolResult> {
    return await this.executeTool('create_bitbucket_repository', params);
  }

  async createBitbucketPullRequest(params: {
    workspace: string;
    repo_slug: string;
    title: string;
    description?: string;
    source: {
      branch: {
        name: string;
      };
    };
    destination: {
      branch: {
        name: string;
      };
    };
    reviewers?: Array<{
      uuid: string;
    }>;
    close_source_branch?: boolean;
  }): Promise<ToolResult> {
    return await this.executeTool('create_bitbucket_pull_request', params);
  }

  async searchBitbucketCode(params: {
    workspace: string;
    repo_slug: string;
    search_query: string;
    page?: number;
    pagelen?: number;
  }): Promise<ToolResult> {
    return await this.executeTool('search_bitbucket_code', params);
  }

  async getBitbucketFileContent(params: {
    workspace: string;
    repo_slug: string;
    commit: string;
    path: string;
    format?: 'raw' | 'rendered';
  }): Promise<ToolResult> {
    return await this.executeTool('get_bitbucket_file_content', params);
  }

  // Utility Methods
  async getJiraProjects(cloudId: string): Promise<ToolResult> {
    return await this.searchJiraIssues({
      cloudId,
      jql: 'project is not EMPTY',
      maxResults: 0,
      fields: ['project']
    });
  }

  async getConfluenceSpaces(cloudId: string): Promise<ToolResult> {
    return await this.searchConfluenceContent({
      cloudId,
      cql: 'type=space',
      limit: 50,
      expand: ['metadata.labels', 'icon', 'description.plain', 'homepage']
    });
  }

  async getBitbucketWorkspaces(): Promise<ToolResult> {
    // This would typically be handled through the accessible resources
    const resources = this.getAccessibleResources();
    const bitbucketResources = resources.filter(r => 
      r.scopes.some(scope => scope.startsWith('repository'))
    );
    
    return {
      success: true,
      data: bitbucketResources,
      metadata: {
        total: bitbucketResources.length,
        service: 'bitbucket'
      }
    };
  }

  // Event Handlers for SSE
  protected handleSSEEvent(event: MessageEvent): void {
    super.handleSSEEvent(event);
    
    try {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case 'jira:issue:updated':
          this.handleJiraIssueUpdate(data.payload);
          break;
        case 'confluence:page:updated':
          this.handleConfluencePageUpdate(data.payload);
          break;
        case 'bitbucket:repository:push':
          this.handleBitbucketPush(data.payload);
          break;
        case 'bitbucket:pullrequest:updated':
          this.handleBitbucketPullRequestUpdate(data.payload);
          break;
        default:
          console.log('Unknown Atlassian SSE event type:', data.type);
      }
    } catch (error) {
      console.error('Error processing Atlassian SSE event:', error);
    }
  }

  private handleJiraIssueUpdate(payload: any): void {
    console.log('Jira issue updated:', payload);
    // Emit event for UI updates
    this.emit('jira:issue:updated', payload);
  }

  private handleConfluencePageUpdate(payload: any): void {
    console.log('Confluence page updated:', payload);
    // Emit event for UI updates
    this.emit('confluence:page:updated', payload);
  }

  private handleBitbucketPush(payload: any): void {
    console.log('Bitbucket repository push:', payload);
    // Emit event for UI updates
    this.emit('bitbucket:repository:push', payload);
  }

  private handleBitbucketPullRequestUpdate(payload: any): void {
    console.log('Bitbucket pull request updated:', payload);
    // Emit event for UI updates
    this.emit('bitbucket:pullrequest:updated', payload);
  }
}

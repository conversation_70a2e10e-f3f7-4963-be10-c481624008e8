export interface MCPConfiguration {
  $schema?: string;
  version: string;
  servers: Record<string, ServerConfiguration>;
  global: GlobalConfiguration;
}

export interface ServerConfiguration {
  transport: 'http-sse' | 'stdio' | 'websocket';
  enabled: boolean;
  endpoints?: {
    http: string;
    sse: string;
    discovery?: string;
  };
  stdio?: {
    executable: string;
    args?: string[];
    cwd?: string;
    env?: Record<string, string>;
    restart_delay?: number;
    max_restarts?: number;
  };
  auth: {
    type: 'oauth' | 'api-key' | 'none';
    client_id: string;
    redirect_uri: string;
    scopes: string[];
    pkce: boolean;
    authorization_endpoint?: string;
    token_endpoint?: string;
  };
  capabilities: ('tools' | 'resources' | 'prompts' | 'notifications')[];
  tools: string[];
  connection: {
    timeout: number;
    retry_attempts: number;
    retry_delay: number;
  };
  cache: {
    enabled: boolean;
    ttl: number;
  };
}

export interface GlobalConfiguration {
  discovery: {
    enabled: boolean;
    timeout: number;
    cache_duration: number;
  };
  security: {
    validate_certificates: boolean;
    require_pkce: boolean;
    allowed_redirect_schemes: string[];
  };
}

export interface MCPConfigurationValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface MCPConfigurationValidationResult {
  valid: boolean;
  errors: MCPConfigurationValidationError[];
}

export const DEFAULT_MCP_CONFIGURATION: MCPConfiguration = {
  version: "1.0",
  servers: {},
  global: {
    discovery: {
      enabled: true,
      timeout: 10000,
      cache_duration: 3600000
    },
    security: {
      validate_certificates: true,
      require_pkce: true,
      allowed_redirect_schemes: ["thealpinecode.alpineintellect"]
    }
  }
};

export const GITHUB_SERVER_CONFIG: ServerConfiguration = {
  transport: "http-sse",
  enabled: true,
  endpoints: {
    http: "https://api.githubcopilot.com/mcp/",
    sse: "https://api.githubcopilot.com/mcp/events"
  },
  auth: {
    type: "oauth",
    client_id: "${ENV:GITHUB_CLIENT_ID}",
    redirect_uri: "thealpinecode.alpineintellect://auth-callback/github",
    scopes: ["repo", "user", "read:org"],
    pkce: true,
    authorization_endpoint: "https://github.com/login/oauth/authorize",
    token_endpoint: "https://github.com/login/oauth/access_token"
  },
  capabilities: ["tools", "notifications"],
  tools: ["create_repository", "create_issue", "search_code", "get_file_content"],
  connection: {
    timeout: 30000,
    retry_attempts: 3,
    retry_delay: 1000
  },
  cache: {
    enabled: true,
    ttl: 300000
  }
};

export const FIGMA_SERVER_CONFIG: ServerConfiguration = {
  transport: "stdio",
  enabled: true,
  stdio: {
    executable: "node",
    args: ["figma-mcp-server.js"],
    cwd: undefined, // Will be set to MCP servers directory
    env: {
      // OAuth tokens will be injected at runtime
      FIGMA_CLIENT_ID: "${ENV:FIGMA_CLIENT_ID}"
    },
    restart_delay: 2000,
    max_restarts: 3
  },
  auth: {
    type: "oauth",
    client_id: "${ENV:FIGMA_CLIENT_ID}",
    redirect_uri: "thealpinecode.alpineintellect://auth-callback/figma",
    scopes: ["file:read", "file:write", "team:read"],
    pkce: true,
    authorization_endpoint: "https://www.figma.com/oauth",
    token_endpoint: "https://www.figma.com/api/oauth/token"
  },
  capabilities: ["tools", "resources", "notifications"],
  tools: [], // Will be discovered dynamically
  connection: {
    timeout: 30000,
    retry_attempts: 3,
    retry_delay: 1000
  },
  cache: {
    enabled: true,
    ttl: 300000
  }
};

export const ATLASSIAN_SERVER_CONFIG: ServerConfiguration = {
  transport: "http-sse",
  enabled: true,
  endpoints: {
    http: "https://api.atlassian.com",
    sse: "https://api.atlassian.com/events",
    discovery: "https://auth.atlassian.com/.well-known/oauth-authorization-server"
  },
  auth: {
    type: "oauth",
    client_id: "${ENV:ATLASSIAN_CLIENT_ID}",
    redirect_uri: "thealpinecode.alpineintellect://auth-callback/atlassian",
    scopes: [
      "read:jira-work",
      "write:jira-work",
      "read:jira-user",
      "read:confluence-content.all",
      "write:confluence-content",
      "read:confluence-space.summary",
      "repository:read",
      "repository:write",
      "pullrequest:read",
      "pullrequest:write",
      "issue:read",
      "issue:write"
    ],
    pkce: true,
    authorization_endpoint: "https://auth.atlassian.com/authorize",
    token_endpoint: "https://auth.atlassian.com/oauth/token"
  },
  capabilities: ["tools", "resources", "notifications"],
  tools: [
    "create_jira_issue",
    "update_jira_issue",
    "search_jira_issues",
    "get_jira_issue",
    "create_confluence_page",
    "update_confluence_page",
    "search_confluence_content",
    "get_confluence_page",
    "create_bitbucket_repository",
    "create_bitbucket_pull_request",
    "search_bitbucket_code",
    "get_bitbucket_file_content",
    "list_accessible_resources"
  ],
  connection: {
    timeout: 30000,
    retry_attempts: 3,
    retry_delay: 1000
  },
  cache: {
    enabled: true,
    ttl: 300000
  }
};

export const DEFAULT_MCP_CONFIGURATION_WITH_SERVICES: MCPConfiguration = {
  ...DEFAULT_MCP_CONFIGURATION,
  servers: {
    github: GITHUB_SERVER_CONFIG,
    figma: FIGMA_SERVER_CONFIG,
    atlassian: ATLASSIAN_SERVER_CONFIG
  }
};

// JSON Schema for validation
export const MCP_CONFIGURATION_SCHEMA = {
  type: "object",
  required: ["version", "servers", "global"],
  properties: {
    $schema: { type: "string" },
    version: { type: "string" },
    servers: {
      type: "object",
      patternProperties: {
        "^[a-zA-Z][a-zA-Z0-9_-]*$": {
          type: "object",
          required: ["transport", "enabled", "auth", "capabilities", "tools", "connection", "cache"],
          properties: {
            transport: { enum: ["http-sse", "stdio", "websocket"] },
            enabled: { type: "boolean" },
            endpoints: {
              type: "object",
              properties: {
                http: { type: "string", format: "uri" },
                sse: { type: "string", format: "uri" },
                discovery: { type: "string", format: "uri" }
              }
            },
            stdio: {
              type: "object",
              properties: {
                executable: { type: "string" },
                args: {
                  type: "array",
                  items: { type: "string" }
                },
                cwd: { type: "string" },
                env: {
                  type: "object",
                  additionalProperties: { type: "string" }
                },
                restart_delay: { type: "number", minimum: 0 },
                max_restarts: { type: "number", minimum: 0 }
              },
              required: ["executable"]
            },
            auth: {
              type: "object",
              required: ["type", "client_id", "redirect_uri", "scopes", "pkce"],
              properties: {
                type: { enum: ["oauth", "api-key", "none"] },
                client_id: { type: "string" },
                redirect_uri: { type: "string", format: "uri" },
                scopes: { type: "array", items: { type: "string" } },
                pkce: { type: "boolean" },
                authorization_endpoint: { type: "string", format: "uri" },
                token_endpoint: { type: "string", format: "uri" }
              }
            },
            capabilities: {
              type: "array",
              items: { enum: ["tools", "resources", "prompts", "notifications"] }
            },
            tools: { type: "array", items: { type: "string" } },
            connection: {
              type: "object",
              required: ["timeout", "retry_attempts", "retry_delay"],
              properties: {
                timeout: { type: "number", minimum: 1000 },
                retry_attempts: { type: "number", minimum: 0 },
                retry_delay: { type: "number", minimum: 0 }
              }
            },
            cache: {
              type: "object",
              required: ["enabled", "ttl"],
              properties: {
                enabled: { type: "boolean" },
                ttl: { type: "number", minimum: 0 }
              }
            }
          }
        }
      }
    },
    global: {
      type: "object",
      required: ["discovery", "security"],
      properties: {
        discovery: {
          type: "object",
          required: ["enabled", "timeout", "cache_duration"],
          properties: {
            enabled: { type: "boolean" },
            timeout: { type: "number", minimum: 1000 },
            cache_duration: { type: "number", minimum: 0 }
          }
        },
        security: {
          type: "object",
          required: ["validate_certificates", "require_pkce", "allowed_redirect_schemes"],
          properties: {
            validate_certificates: { type: "boolean" },
            require_pkce: { type: "boolean" },
            allowed_redirect_schemes: { type: "array", items: { type: "string" } }
          }
        }
      }
    }
  }
};

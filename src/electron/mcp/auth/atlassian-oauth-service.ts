import { OAuthService, OAuthConfig } from './oauth-service.js';

export interface AtlassianOAuthConfig extends OAuthConfig {
  audience?: string;
}

export class AtlassianOAuthService extends OAuthService {
  constructor(config: AtlassianOAuthConfig) {
    super({
      serviceId: 'atlassian',
      clientId: config.clientId,
      authorizationEndpoint: config.authorizationEndpoint || 'https://auth.atlassian.com/authorize',
      tokenEndpoint: config.tokenEndpoint || 'https://auth.atlassian.com/oauth/token',
      redirectUri: config.redirectUri,
      scopes: config.scopes,
      pkce: config.pkce !== false // Default to true
    });
  }

  async generateAuthorizationUrl(): Promise<string> {
    console.log('Generating Atlassian OAuth authorization URL...');

    // Use the base class method but add Atlassian-specific parameters
    const baseUrl = await this.initializeOAuth();
    const url = new URL(baseUrl);

    // Add Atlassian-specific parameters
    url.searchParams.set('audience', 'api.atlassian.com');
    url.searchParams.set('prompt', 'consent');

    console.log('✅ Atlassian OAuth authorization URL generated');
    return url.toString();
  }
}

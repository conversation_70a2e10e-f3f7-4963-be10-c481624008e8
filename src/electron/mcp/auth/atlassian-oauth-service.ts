import crypto from 'crypto';
import keytar from 'keytar';
import { OAuthService, OAuthConfig, TokenResponse } from './oauth-service.js';
import { TokenExchangeService, TokenExchangeRequest, TokenRefreshRequest } from './token-exchange-service.js';

export interface OAuthResult {
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  scope?: string;
  error?: string;
}

export interface AtlassianOAuthConfig extends OAuthConfig {
  audience?: string;
}

export class AtlassianOAuthService extends OAuthService {
  private readonly KEYTAR_SERVICE = 'alpine-app';
  private readonly tokenExchangeService: TokenExchangeService;

  constructor(config: AtlassianOAuthConfig) {
    super({
      serviceId: 'atlassian',
      clientId: config.clientId,
      authorizationEndpoint: config.authorizationEndpoint || 'https://auth.atlassian.com/authorize',
      tokenEndpoint: config.tokenEndpoint || 'https://auth.atlassian.com/oauth/token',
      redirectUri: config.redirectUri,
      scopes: config.scopes,
      pkce: config.pkce !== false // Default to true
    });

    this.tokenExchangeService = new TokenExchangeService();
  }

  async generateAuthorizationUrl(): Promise<string> {
    console.log('Generating Atlassian OAuth authorization URL...');

    try {
      // Generate PKCE parameters
      const codeVerifier = this.generateCodeVerifier();
      const codeChallenge = await this.generateCodeChallenge(codeVerifier);
      const state = this.generateState();

      // Store PKCE parameters using same keytar service as existing auth
      await keytar.setPassword(this.KEYTAR_SERVICE, 'atlassian-pkce-verifier', codeVerifier);
      await keytar.setPassword(this.KEYTAR_SERVICE, 'atlassian-oauth-state', state);

      // Build authorization URL with Atlassian-specific parameters
      const authUrl = new URL(this.config.authorizationEndpoint);
      authUrl.searchParams.set('client_id', this.config.clientId);
      authUrl.searchParams.set('redirect_uri', this.config.redirectUri);
      authUrl.searchParams.set('scope', this.config.scopes.join(' '));
      authUrl.searchParams.set('response_type', 'code');
      authUrl.searchParams.set('state', state);
      authUrl.searchParams.set('code_challenge', codeChallenge);
      authUrl.searchParams.set('code_challenge_method', 'S256');
      authUrl.searchParams.set('audience', 'api.atlassian.com');
      authUrl.searchParams.set('prompt', 'consent');

      console.log('✅ Atlassian OAuth authorization URL generated');
      return authUrl.toString();

    } catch (error) {
      console.error('❌ Failed to generate Atlassian OAuth authorization URL:', error);
      throw error;
    }
  }

  async exchangeCodeForTokens(
    authorizationCode: string, 
    receivedState: string
  ): Promise<OAuthResult> {
    console.log('Exchanging Atlassian authorization code for tokens...');

    try {
      // Validate state parameter using same keytar service
      const storedState = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-oauth-state');
      if (receivedState !== storedState) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      // Get stored PKCE verifier using same keytar service
      const codeVerifier = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-pkce-verifier');
      if (!codeVerifier) {
        throw new Error('PKCE code verifier not found');
      }

      // Get client secret from keytar
      const clientSecret = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-client-secret');
      if (!clientSecret) {
        throw new Error('Atlassian client secret not found in secure storage');
      }

      // Prepare token exchange request
      const tokenRequest: TokenExchangeRequest = {
        serviceId: 'atlassian',
        authorizationCode,
        state: receivedState,
        codeVerifier,
        clientId: this.config.clientId,
        clientSecret,
        redirectUri: this.config.redirectUri,
        tokenEndpoint: this.config.tokenEndpoint
      };

      // Exchange code for tokens
      const tokenResponse = await this.tokenExchangeService.exchangeCodeForTokens(tokenRequest);

      console.log('✅ Atlassian OAuth tokens obtained successfully');
      return {
        success: true,
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        expiresIn: tokenResponse.expires_in,
        scope: tokenResponse.scope
      };

    } catch (error) {
      console.error('❌ Atlassian OAuth token exchange failed:', error);
      
      // Clean up OAuth state on failure
      await this.cleanupOAuthState();
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  async refreshAccessToken(): Promise<OAuthResult> {
    console.log('Refreshing Atlassian access token...');

    try {
      const refreshToken = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-refresh-token');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const clientSecret = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-client-secret');
      if (!clientSecret) {
        throw new Error('Client secret not found');
      }

      const refreshRequest: TokenRefreshRequest = {
        serviceId: 'atlassian',
        refreshToken,
        clientId: this.config.clientId,
        clientSecret,
        tokenEndpoint: this.config.tokenEndpoint
      };

      const tokenResponse = await this.tokenExchangeService.refreshTokens(refreshRequest);

      console.log('✅ Atlassian access token refreshed successfully');
      return {
        success: true,
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        expiresIn: tokenResponse.expires_in,
        scope: tokenResponse.scope
      };

    } catch (error) {
      console.error('❌ Atlassian token refresh failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  async getAccessToken(): Promise<string | null> {
    try {
      const accessToken = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-access-token');
      
      if (!accessToken) {
        return null;
      }

      // Check if token is expired and refresh if needed
      const isExpired = await this.isTokenExpired();
      if (isExpired) {
        const refreshResult = await this.refreshAccessToken();
        if (refreshResult.success) {
          return refreshResult.accessToken || null;
        } else {
          return null;
        }
      }

      return accessToken;
    } catch (error) {
      console.error('Failed to get Atlassian access token:', error);
      return null;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    const accessToken = await this.getAccessToken();
    return accessToken !== null;
  }

  async revokeAuthentication(): Promise<void> {
    console.log('Revoking Atlassian authentication...');

    try {
      // Remove all stored tokens and OAuth state
      await keytar.deletePassword(this.KEYTAR_SERVICE, 'atlassian-access-token');
      await keytar.deletePassword(this.KEYTAR_SERVICE, 'atlassian-refresh-token');
      await keytar.deletePassword(this.KEYTAR_SERVICE, 'atlassian-token-expiry');
      await this.cleanupOAuthState();

      console.log('✅ Atlassian authentication revoked successfully');
    } catch (error) {
      console.error('❌ Failed to revoke Atlassian authentication:', error);
      throw error;
    }
  }

  private async isTokenExpired(): Promise<boolean> {
    try {
      const expiryStr = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-token-expiry');
      if (!expiryStr) {
        return true; // No expiry info means we should refresh
      }

      const expiry = new Date(expiryStr);
      const now = new Date();
      const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

      return expiry <= fiveMinutesFromNow;
    } catch (error) {
      console.error('Error checking token expiry:', error);
      return true; // Assume expired on error
    }
  }

  private async cleanupOAuthState(): Promise<void> {
    try {
      await keytar.deletePassword(this.KEYTAR_SERVICE, 'atlassian-pkce-verifier');
      await keytar.deletePassword(this.KEYTAR_SERVICE, 'atlassian-oauth-state');
    } catch (error) {
      console.warn('Failed to cleanup OAuth state:', error);
    }
  }

  private generateCodeVerifier(): string {
    // Generate cryptographically secure random string (128 characters)
    return crypto.randomBytes(96).toString('base64url');
  }

  private async generateCodeChallenge(codeVerifier: string): Promise<string> {
    // Create SHA256 hash of code verifier and encode as base64url
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    return hash.toString('base64url');
  }

  private generateState(): string {
    // Generate cryptographically secure random string (32 characters)
    return crypto.randomBytes(24).toString('base64url');
  }

  async getAuthStatus(): Promise<{
    authenticated: boolean;
    expiresAt?: Date;
    scopes?: string[];
  }> {
    try {
      const isAuth = await this.isAuthenticated();
      if (!isAuth) {
        return { authenticated: false };
      }

      const expiryStr = await keytar.getPassword(this.KEYTAR_SERVICE, 'atlassian-token-expiry');
      const expiresAt = expiryStr ? new Date(expiryStr) : undefined;

      return {
        authenticated: true,
        expiresAt,
        scopes: this.config.scopes
      };
    } catch (error) {
      console.error('Error getting auth status:', error);
      return { authenticated: false };
    }
  }
}

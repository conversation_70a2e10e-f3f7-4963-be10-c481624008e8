import { OAuthCallbackHandler } from './oauth-callback-router.js';
import { AtlassianOAuthService } from './atlassian-oauth-service.js';

export class AtlassianOAuthCallbackProcessor implements OAuthCallbackHandler {
  readonly serviceId = 'atlassian';
  readonly callbackPath = '/auth-callback/atlassian';
  
  private oauthService: AtlassianOAuthService;

  constructor() {
    // Initialize OAuth service with configuration
    this.oauthService = new AtlassianOAuthService({
      serviceId: 'atlassian',
      clientId: process.env.ATLASSIAN_CLIENT_ID || '',
      authorizationEndpoint: 'https://auth.atlassian.com/authorize',
      tokenEndpoint: 'https://auth.atlassian.com/oauth/token',
      redirectUri: 'thealpinecode.alpineintellect://auth-callback/atlassian',
      scopes: [
        'read:jira-work',
        'write:jira-work',
        'read:jira-user',
        'read:confluence-content.all',
        'write:confluence-content',
        'read:confluence-space.summary',
        'repository:read',
        'repository:write',
        'pullrequest:read',
        'pullrequest:write',
        'issue:read',
        'issue:write'
      ],
      pkce: true
    });
  }

  async handleCallback(params: URLSearchParams): Promise<void> {
    console.log('Processing Atlassian OAuth callback...');

    try {
      // Extract parameters from callback URL
      const code = params.get('code');
      const state = params.get('state');
      const error = params.get('error');
      const errorDescription = params.get('error_description');

      // Handle OAuth errors
      if (error) {
        console.error('Atlassian OAuth error:', error, errorDescription);
        throw new Error(`OAuth error: ${error} - ${errorDescription || 'Unknown error'}`);
      }

      // Validate required parameters
      if (!code) {
        throw new Error('Authorization code not found in callback');
      }

      if (!state) {
        throw new Error('State parameter not found in callback');
      }

      // Exchange authorization code for tokens
      await this.oauthService.exchangeCodeForTokens(code, state);

      console.log('✅ Atlassian OAuth callback processed successfully');

      // Notify the main window about successful authentication
      await this.notifyAuthenticationSuccess();

    } catch (error) {
      console.error('❌ Atlassian OAuth callback processing failed:', error);
      
      // Notify the main window about authentication failure
      await this.notifyAuthenticationFailure(error instanceof Error ? error.message : String(error));
      
      throw error;
    }
  }

  private async notifyAuthenticationSuccess(): Promise<void> {
    try {
      // Import dynamically to avoid circular dependencies
      const { BrowserWindow } = await import('electron');
      const mainWindow = BrowserWindow.getAllWindows()[0];

      if (mainWindow) {
        // Show and focus the window
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        mainWindow.show();
        mainWindow.focus();
        mainWindow.setAlwaysOnTop(true);
        setTimeout(() => {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.setAlwaysOnTop(false);
          }
        }, 1000);

        // Send success notification to renderer
        mainWindow.webContents.send('atlassian-oauth-callback', {
          success: true,
          message: 'Successfully authenticated with Atlassian'
        });
      }
    } catch (error) {
      console.error('Failed to notify authentication success:', error);
    }
  }

  private async notifyAuthenticationFailure(errorMessage: string): Promise<void> {
    try {
      // Import dynamically to avoid circular dependencies
      const { BrowserWindow } = await import('electron');
      const mainWindow = BrowserWindow.getAllWindows()[0];

      if (mainWindow) {
        // Show and focus the window
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        mainWindow.show();
        mainWindow.focus();
        mainWindow.setAlwaysOnTop(true);
        setTimeout(() => {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.setAlwaysOnTop(false);
          }
        }, 1000);

        // Send failure notification to renderer
        mainWindow.webContents.send('atlassian-oauth-callback', {
          success: false,
          error: errorMessage,
          message: 'Atlassian authentication failed'
        });
      }
    } catch (error) {
      console.error('Failed to notify authentication failure:', error);
    }
  }

  async generateAuthorizationUrl(): Promise<string> {
    return await this.oauthService.generateAuthorizationUrl();
  }

  async getAuthStatus(): Promise<{
    authenticated: boolean;
    expiresAt?: Date;
    scopes?: string[];
  }> {
    const authStatus = await this.oauthService.getAuthStatus();
    return {
      authenticated: authStatus.authenticated,
      expiresAt: authStatus.expiresAt,
      scopes: authStatus.scopes
    };
  }

  async revokeAuthentication(): Promise<void> {
    await this.oauthService.revokeAccess();
  }

  async isAuthenticated(): Promise<boolean> {
    const authStatus = await this.oauthService.getAuthStatus();
    return authStatus.authenticated;
  }

  async getAccessToken(): Promise<string | null> {
    try {
      return await this.oauthService.getAccessToken();
    } catch (error) {
      // Base class throws error if no token, we return null for compatibility
      return null;
    }
  }
}

import { MCPHost } from '../electron/mcp/host/mcp-host.js';

export interface CodeAssistantCapabilities {
  repositoryManagement: boolean;
  codeSearch: boolean;
  issueTracking: boolean;
  pullRequestManagement: boolean;
  documentation: boolean;
  projectManagement: boolean;
}

export interface CodeAssistantRequest {
  type: 'create_repo' | 'search_code' | 'create_issue' | 'get_file' | 'analyze_repo';
  parameters: any;
  context?: {
    projectPath?: string;
    language?: string;
    framework?: string;
  };
}

export interface CodeAssistantResponse {
  success: boolean;
  data?: any;
  error?: string;
  suggestions?: string[];
}

export class CodeAssistantAgent {
  private mcpHost: MCPHost;
  private capabilities: CodeAssistantCapabilities;

  constructor(mcpHost: MCPHost) {
    this.mcpHost = mcpHost;
    this.capabilities = {
      repositoryManagement: false,
      codeSearch: false,
      issueTracking: false,
      pullRequestManagement: false,
      documentation: false,
      projectManagement: false
    };
  }

  async initialize(): Promise<void> {
    console.log('Initializing Code Assistant Agent...');

    try {
      // Check GitHub service availability
      const githubStatus = await this.mcpHost.getServiceStatus('github');

      // Check Atlassian service availability
      const atlassianStatus = await this.mcpHost.getServiceStatus('atlassian');

      // Initialize capabilities based on available services
      this.capabilities = {
        repositoryManagement: false,
        codeSearch: false,
        issueTracking: false,
        pullRequestManagement: false,
        documentation: false,
        projectManagement: false
      };

      // GitHub capabilities
      if (githubStatus.authenticated && githubStatus.healthy) {
        this.capabilities.repositoryManagement = githubStatus.availableTools.includes('create_repository');
        this.capabilities.codeSearch = githubStatus.availableTools.includes('search_code');
        this.capabilities.issueTracking = githubStatus.availableTools.includes('create_issue');
        this.capabilities.pullRequestManagement = githubStatus.availableTools.includes('create_pull_request');

        console.log('✅ Code Assistant Agent initialized with GitHub integration');
      }

      // Atlassian capabilities
      if (atlassianStatus.authenticated && atlassianStatus.healthy) {
        // Jira capabilities
        if (atlassianStatus.availableTools.includes('create_jira_issue')) {
          this.capabilities.issueTracking = true;
          this.capabilities.projectManagement = true;
        }

        // Confluence capabilities
        if (atlassianStatus.availableTools.includes('create_confluence_page')) {
          this.capabilities.documentation = true;
        }

        // Bitbucket capabilities (enhance existing capabilities)
        if (atlassianStatus.availableTools.includes('create_bitbucket_repository')) {
          this.capabilities.repositoryManagement = true;
        }
        if (atlassianStatus.availableTools.includes('search_bitbucket_code')) {
          this.capabilities.codeSearch = true;
        }
        if (atlassianStatus.availableTools.includes('create_bitbucket_pull_request')) {
          this.capabilities.pullRequestManagement = true;
        }

        console.log('✅ Code Assistant Agent initialized with Atlassian integration');
      }

      if (!githubStatus.authenticated && !atlassianStatus.authenticated) {
        console.log('⚠️ Code Assistant Agent initialized without service integrations');
      }

    } catch (error) {
      console.error('❌ Failed to initialize Code Assistant Agent:', error);
    }
  }

  async processRequest(request: CodeAssistantRequest): Promise<CodeAssistantResponse> {
    console.log(`Processing code assistant request: ${request.type}`);

    try {
      switch (request.type) {
        case 'create_repo':
          return await this.createRepository(request.parameters);
        
        case 'search_code':
          return await this.searchCode(request.parameters);
        
        case 'create_issue':
          return await this.createIssue(request.parameters);
        
        case 'get_file':
          return await this.getFileContent(request.parameters);
        
        case 'analyze_repo':
          return await this.analyzeRepository(request.parameters);
        
        default:
          return {
            success: false,
            error: `Unknown request type: ${request.type}`
          };
      }
    } catch (error) {
      console.error(`Code assistant request failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async createRepository(params: any): Promise<CodeAssistantResponse> {
    if (!this.capabilities.repositoryManagement) {
      return {
        success: false,
        error: 'Repository management not available. Please connect to GitHub.'
      };
    }

    const result = await this.mcpHost.executeTool('github', 'create_repository', {
      name: params.name,
      description: params.description || 'Created with Alpine Intellect',
      private: params.private || false,
      auto_init: true
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Clone the repository to start working',
        'Set up CI/CD workflows',
        'Add a comprehensive README'
      ]
    };
  }

  private async searchCode(params: any): Promise<CodeAssistantResponse> {
    if (!this.capabilities.codeSearch) {
      return {
        success: false,
        error: 'Code search not available. Please connect to GitHub.'
      };
    }

    const result = await this.mcpHost.executeTool('github', 'search_code', {
      query: params.query,
      sort: 'indexed',
      per_page: params.limit || 10
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Refine your search query for better results',
        'Use specific file extensions or languages',
        'Try searching in specific repositories'
      ]
    };
  }

  private async createIssue(params: any): Promise<CodeAssistantResponse> {
    if (!this.capabilities.issueTracking) {
      return {
        success: false,
        error: 'Issue tracking not available. Please connect to GitHub.'
      };
    }

    const result = await this.mcpHost.executeTool('github', 'create_issue', {
      owner: params.owner,
      repo: params.repo,
      title: params.title,
      body: params.body || '',
      labels: params.labels || []
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Add relevant labels to categorize the issue',
        'Assign team members if needed',
        'Link related issues or pull requests'
      ]
    };
  }

  private async getFileContent(params: any): Promise<CodeAssistantResponse> {
    const result = await this.mcpHost.executeTool('github', 'get_file_content', {
      owner: params.owner,
      repo: params.repo,
      path: params.path,
      ref: params.ref || 'main'
    });

    return {
      success: true,
      data: result,
      suggestions: [
        'Analyze the code structure',
        'Check for potential improvements',
        'Review for security issues'
      ]
    };
  }

  private async analyzeRepository(params: any): Promise<CodeAssistantResponse> {
    // This would be a more complex operation that combines multiple tools
    const analyses = [];

    try {
      // Get repository structure
      const repoInfo = await this.mcpHost.executeTool('github', 'get_file_content', {
        owner: params.owner,
        repo: params.repo,
        path: 'README.md'
      });
      analyses.push({ type: 'readme', data: repoInfo });

      // Search for common files
      const packageJson = await this.mcpHost.executeTool('github', 'search_code', {
        query: `repo:${params.owner}/${params.repo} filename:package.json`
      });
      if (packageJson) {
        analyses.push({ type: 'package_json', data: packageJson });
      }

      return {
        success: true,
        data: {
          repository: `${params.owner}/${params.repo}`,
          analyses
        },
        suggestions: [
          'Review the project structure',
          'Check dependencies for updates',
          'Analyze code quality metrics',
          'Review security vulnerabilities'
        ]
      };
    } catch (error) {
      return {
        success: false,
        error: `Repository analysis failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  getCapabilities(): CodeAssistantCapabilities {
    return { ...this.capabilities };
  }

  async refreshCapabilities(): Promise<void> {
    await this.initialize();
  }

  isAvailable(): boolean {
    return Object.values(this.capabilities).some(capability => capability);
  }

  getSupportedOperations(): string[] {
    const operations: string[] = [];
    
    if (this.capabilities.repositoryManagement) {
      operations.push('create_repo', 'analyze_repo');
    }
    
    if (this.capabilities.codeSearch) {
      operations.push('search_code', 'get_file');
    }
    
    if (this.capabilities.issueTracking) {
      operations.push('create_issue');
    }
    
    return operations;
  }
}

{"$schema": "https://schemas.alpine-intellect.com/mcp-config.schema.json", "version": "1.0", "servers": {"github": {"transport": "http-sse", "enabled": true, "endpoints": {"http": "https://api.githubcopilot.com/mcp/", "sse": "https://api.githubcopilot.com/mcp/events"}, "auth": {"type": "o<PERSON>h", "client_id": "Ov23liAidGUWPcGcGjWm", "redirect_uri": "thealpinecode.alpineintellect://auth-callback/github", "scopes": ["repo", "user", "read:org"], "pkce": true, "authorization_endpoint": "https://github.com/login/oauth/authorize", "token_endpoint": "https://github.com/login/oauth/access_token"}, "capabilities": ["tools", "notifications"], "tools": ["create_repository", "create_issue", "search_code", "get_file_content"], "connection": {"timeout": 30000, "retry_attempts": 3, "retry_delay": 1000}, "cache": {"enabled": true, "ttl": 300000}}, "figma": {"transport": "stdio", "enabled": true, "stdio": {"executable": "node", "args": ["figma-mcp-server.js"], "env": {"FIGMA_CLIENT_ID": "your_figma_client_id_here"}, "restart_delay": 2000, "max_restarts": 3}, "auth": {"type": "o<PERSON>h", "client_id": "your_figma_client_id_here", "redirect_uri": "thealpinecode.alpineintellect://auth-callback/figma", "scopes": ["file:read", "file:write", "team:read"], "pkce": true, "authorization_endpoint": "https://www.figma.com/oauth", "token_endpoint": "https://www.figma.com/api/oauth/token"}, "capabilities": ["tools", "resources", "notifications"], "tools": [], "connection": {"timeout": 30000, "retry_attempts": 3, "retry_delay": 1000}, "cache": {"enabled": true, "ttl": 300000}}, "atlassian": {"transport": "http-sse", "enabled": true, "endpoints": {"http": "https://api.atlassian.com/mcp/v1", "sse": "https://api.atlassian.com/mcp/v1/events", "discovery": "https://api.atlassian.com/mcp/.well-known/oauth-authorization-server"}, "auth": {"type": "o<PERSON>h", "client_id": "${ENV:ATLASSIAN_CLIENT_ID}", "redirect_uri": "thealpinecode.alpineintellect://auth-callback/atlassian", "scopes": ["read:jira-work", "write:jira-work", "read:jira-user", "read:confluence-content.all", "write:confluence-content", "read:confluence-space.summary", "repository:read", "repository:write", "pullrequest:read", "pullrequest:write", "issue:read", "issue:write"], "pkce": true, "authorization_endpoint": "https://auth.atlassian.com/authorize", "token_endpoint": "https://auth.atlassian.com/oauth/token"}, "capabilities": ["tools", "resources", "notifications"], "tools": ["create_jira_issue", "update_jira_issue", "search_jira_issues", "get_jira_issue", "create_confluence_page", "update_confluence_page", "search_confluence_content", "get_confluence_page", "create_bitbucket_repository", "create_bitbucket_pull_request", "search_bitbucket_code", "get_bitbucket_file_content", "list_accessible_resources"], "connection": {"timeout": 30000, "retry_attempts": 3, "retry_delay": 1000}, "cache": {"enabled": true, "ttl": 300000}}}, "global": {"discovery": {"enabled": true, "timeout": 10000, "cache_duration": 3600000}, "security": {"validate_certificates": true, "require_pkce": true, "allowed_redirect_schemes": ["thealpinecode.alpineintellect"]}}}
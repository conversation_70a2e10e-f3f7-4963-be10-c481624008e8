# MCP Service Integration Pattern - Comprehensive Implementation Prompt

## Overview

This prompt provides a complete template for implementing OAuth 2.0 + PKCE + HTTP/SSE service integrations in the Alpine Intellect MCP system. It's based on the proven patterns established for GitHub and Atlassian integrations and serves as a reusable template for any remote service integration.

## Implementation Pattern Structure

### 1. MCP Configuration Setup

**File**: `mcp-config.json` and `src/electron/mcp/config/mcp-config-types.ts`

```typescript
// Add to mcp-config-types.ts
export const {SERVICE_NAME}_SERVER_CONFIG: ServerConfiguration = {
  transport: "http-sse",
  enabled: true,
  endpoints: {
    http: "https://api.{service}.com/mcp/v1",
    sse: "https://api.{service}.com/mcp/v1/events",
    discovery: "https://api.{service}.com/.well-known/oauth-authorization-server"
  },
  auth: {
    type: "oauth",
    client_id: "${ENV:{SERVICE_NAME}_CLIENT_ID}",
    redirect_uri: "thealpinecode.alpineintellect://auth-callback/{service}",
    scopes: ["{service_specific_scopes}"],
    pkce: true,
    authorization_endpoint: "https://{service}.com/oauth/authorize",
    token_endpoint: "https://{service}.com/oauth/token"
  },
  capabilities: ["tools", "resources", "notifications"],
  tools: ["{service_specific_tools}"],
  connection: {
    timeout: 30000,
    retry_attempts: 3,
    retry_delay: 1000
  },
  cache: {
    enabled: true,
    ttl: 300000
  }
};
```

### 2. OAuth 2.0 + PKCE Authentication Flow

**Implementation Pattern**:

#### Stage 1: Authorization URL Generation
- Generate cryptographically secure PKCE code verifier (128 chars)
- Create SHA256 code challenge (base64url encoded)
- Generate secure state parameter (32 chars) for CSRF protection
- Construct authorization URL with all security parameters

#### Stage 2: Deep Link Callback Handling
- Extend existing `handleDeepLink` function in `src/electron/main.ts`
- Create service-specific callback processor: `src/mcp/auth/{service}-oauth-callback-processor.ts`
- Validate state parameter to prevent CSRF attacks
- Extract authorization code from callback URL

#### Stage 3: Token Exchange
- Exchange authorization code for access/refresh tokens
- Use PKCE code verifier for additional security
- Store tokens securely in Keytar with service namespace
- Implement automatic token refresh mechanism

#### Stage 4: Secure Token Storage
```typescript
// Keytar storage pattern
service_name: "alpine-app"  // Consistent across all services
access_token: "${KEYTAR:alpine-app:{service}-access-token}"
refresh_token: "${KEYTAR:alpine-app:{service}-refresh-token}"
client_secret: "${KEYTAR:alpine-app:{service}-client-secret}"
```

### 3. Service Client Implementation

**File**: `src/electron/mcp/clients/{service}-service.ts`

```typescript
import { RemoteServiceClient, ToolResult } from './remote-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';
import { AuthorizationServerMetadata } from '../discovery/oauth-discovery-client.js';

export class {Service}Service extends RemoteServiceClient {
  constructor(config: ServerConfiguration) {
    super('{service}', '{Service Display Name}', config);
  }

  setDiscoveredMetadata(metadata: AuthorizationServerMetadata): void {
    this.oauthService.setDiscoveredMetadata(metadata);
    console.log('{Service} OAuth metadata updated from discovery');
  }

  // Service-specific tool implementations
  async {toolName}(params: {ToolParams}): Promise<ToolResult> {
    return await this.executeTool('{tool_name}', params);
  }

  // SSE event handling
  protected handleSSEEvent(event: MessageEvent): void {
    super.handleSSEEvent(event);
    
    try {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case '{service}:{event_type}':
          this.handle{EventType}(data.payload);
          break;
        default:
          console.log('Unknown {Service} SSE event type:', data.type);
      }
    } catch (error) {
      console.error('Error processing {Service} SSE event:', error);
    }
  }

  private handle{EventType}(payload: any): void {
    console.log('{Service} {event} event:', payload);
    this.emit('{service}:{event}', payload);
  }
}
```

### 4. HTTP/SSE Transport Configuration

**Pattern Elements**:
- **HTTP Client**: Base URL, authentication headers, timeout/retry configuration
- **SSE Client**: Event stream endpoint, auto-reconnection, event type handling
- **Authentication**: Bearer token injection, automatic token refresh on 401
- **Error Handling**: Rate limiting, service unavailable, permission errors

### 5. Agent Integration Pattern

**File**: `src/agents/code-assistant-agent.ts`

```typescript
// Capability detection pattern
const {service}Status = await this.mcpHost.getServiceStatus('{service}');

if ({service}Status.authenticated && {service}Status.healthy) {
  this.capabilities = {
    {capability1}: {service}Status.availableTools.includes('{tool1}'),
    {capability2}: {service}Status.availableTools.includes('{tool2}'),
    // ... additional capabilities
  };
  
  console.log('✅ Code Assistant Agent initialized with {Service} integration');
} else {
  console.log('⚠️ Code Assistant Agent initialized without {Service} integration');
}
```

### 6. Security Implementation Checklist

#### OAuth Security
- [ ] PKCE implementation with S256 code challenge method
- [ ] State parameter for CSRF protection (32-char secure random)
- [ ] Secure code verifier generation (128-char secure random)
- [ ] Proper redirect URI validation
- [ ] Token storage in OS-level secure storage (Keytar)

#### API Security
- [ ] Scope validation before tool execution
- [ ] Bearer token authentication headers
- [ ] Automatic token refresh on expiration
- [ ] Rate limiting respect and backoff strategies
- [ ] Resource isolation (only accessible resources)

#### Audit and Monitoring
- [ ] Security audit logging for all API operations
- [ ] Token health monitoring and alerts
- [ ] Failed authentication attempt tracking
- [ ] Suspicious activity detection

### 7. Error Handling Strategy

#### OAuth Error Patterns
```typescript
// Standard OAuth error handling
switch (error.error) {
  case 'access_denied':
    // User denied authorization - show retry option
    break;
  case 'invalid_client':
    // Invalid client configuration - check environment variables
    break;
  case 'invalid_grant':
    // Invalid authorization code - restart OAuth flow
    break;
  case 'invalid_scope':
    // Requested scope not available - adjust requirements
    break;
}
```

#### API Error Patterns
```typescript
// Standard API error handling
switch (response.status) {
  case 401:
    // Unauthorized - trigger token refresh
    await this.oauthService.refreshToken();
    break;
  case 403:
    // Forbidden - show scope requirements
    break;
  case 429:
    // Rate limited - implement exponential backoff
    break;
  case 503:
    // Service unavailable - show service status
    break;
}
```

### 8. Testing Strategy Template

#### Unit Tests
- OAuth flow components (PKCE generation, token exchange, refresh)
- HTTP/SSE transport functionality
- Service client method implementations
- Error handling scenarios

#### Integration Tests
- End-to-end OAuth flow with test credentials
- API tool execution against service test environment
- Real-time notification handling
- Multi-service coordination

#### Manual Testing
- User OAuth flow with real service account
- Code Assistant Agent tool usage
- Error recovery scenarios
- Performance under load

### 9. Implementation Files Checklist

#### Configuration Files
- [ ] `mcp-config.json` - Server configuration entry
- [ ] `src/electron/mcp/config/mcp-config-types.ts` - TypeScript configuration

#### Authentication Files
- [ ] `src/mcp/auth/{service}-oauth-service.ts` - OAuth service implementation
- [ ] `src/mcp/auth/{service}-oauth-callback-processor.ts` - Callback handling
- [ ] `src/mcp/auth/token-exchange-service.ts` - Token exchange logic (if new)

#### Service Client Files
- [ ] `src/electron/mcp/clients/{service}-service.ts` - Main service client
- [ ] `src/mcp/transport/{service}-http-sse-transport.ts` - Transport layer (if custom)

#### Integration Files
- [ ] `src/agents/code-assistant-agent.ts` - Agent integration updates
- [ ] `src/electron/main.ts` - Deep link handler extension

#### Documentation Files
- [ ] `{SERVICE}_OAUTH_IMPLEMENTATION_GUIDE.yaml` - Implementation guide
- [ ] `{SERVICE}_MCP_INTEGRATION_CONFIG.md` - Configuration documentation
- [ ] `{SERVICE}_MCP_INTEGRATION_SUMMARY.md` - Summary and deployment guide

### 10. Environment and Deployment

#### Environment Variables
```bash
# Required environment variables
{SERVICE_NAME}_CLIENT_ID=your_service_client_id

# Keytar secure storage keys
# alpine-app:{service}-client-secret
# alpine-app:{service}-access-token
# alpine-app:{service}-refresh-token
```

#### Deployment Checklist
- [ ] Service OAuth app registered with correct redirect URI
- [ ] Client ID and secret configured
- [ ] Environment variables set
- [ ] Keytar storage initialized
- [ ] MCP configuration updated
- [ ] Service client implemented and tested
- [ ] Agent integration completed
- [ ] Error handling verified
- [ ] Security audit completed

### 11. Service-Specific Customization Points

When implementing a new service, customize these elements:

1. **Service Endpoints**: Update API base URLs and discovery endpoints
2. **OAuth Scopes**: Define service-specific permission scopes
3. **Tool Definitions**: Implement service-specific tools and capabilities
4. **Data Models**: Create TypeScript interfaces for service data structures
5. **SSE Events**: Define real-time event types and handlers
6. **Error Codes**: Map service-specific error codes to standard handling
7. **Rate Limits**: Implement service-specific rate limiting strategies
8. **Resource Discovery**: Handle service-specific resource enumeration

### 12. Quality Assurance

#### Code Review Checklist
- [ ] OAuth implementation follows security best practices
- [ ] Error handling covers all documented error scenarios
- [ ] Token management is secure and follows established patterns
- [ ] Service client methods have proper type definitions
- [ ] SSE event handling is robust and error-resistant
- [ ] Configuration follows established naming conventions
- [ ] Documentation is complete and accurate

#### Security Review Checklist
- [ ] PKCE implementation is cryptographically secure
- [ ] State parameter prevents CSRF attacks
- [ ] Token storage uses OS-level secure storage
- [ ] API calls include proper authentication headers
- [ ] Scope validation prevents privilege escalation
- [ ] Rate limiting prevents abuse
- [ ] Audit logging captures security events

This comprehensive pattern ensures consistent, secure, and maintainable MCP service integrations across the Alpine Intellect platform.

## 13. Real-World Implementation Examples

### GitHub Integration Reference
```typescript
// Example from GitHub implementation
export const GITHUB_SERVER_CONFIG: ServerConfiguration = {
  transport: "http-sse",
  enabled: true,
  endpoints: {
    http: "https://api.githubcopilot.com/mcp/",
    sse: "https://api.githubcopilot.com/mcp/events"
  },
  auth: {
    type: "oauth",
    client_id: "${ENV:GITHUB_CLIENT_ID}",
    redirect_uri: "thealpinecode.alpineintellect://auth-callback/github",
    scopes: ["repo", "user", "read:org"],
    pkce: true,
    authorization_endpoint: "https://github.com/login/oauth/authorize",
    token_endpoint: "https://github.com/login/oauth/access_token"
  },
  capabilities: ["tools", "notifications"],
  tools: ["create_repository", "create_issue", "search_code", "get_file_content"]
};
```

### Atlassian Integration Reference
```typescript
// Example from Atlassian implementation
export const ATLASSIAN_SERVER_CONFIG: ServerConfiguration = {
  transport: "http-sse",
  enabled: true,
  endpoints: {
    http: "https://api.atlassian.com/mcp/v1",
    sse: "https://api.atlassian.com/mcp/v1/events",
    discovery: "https://api.atlassian.com/mcp/.well-known/oauth-authorization-server"
  },
  auth: {
    type: "oauth",
    client_id: "${ENV:ATLASSIAN_CLIENT_ID}",
    redirect_uri: "thealpinecode.alpineintellect://auth-callback/atlassian",
    scopes: [
      "read:jira-work", "write:jira-work", "read:jira-user",
      "read:confluence-content.all", "write:confluence-content",
      "repository:read", "repository:write", "pullrequest:read"
    ],
    pkce: true,
    authorization_endpoint: "https://auth.atlassian.com/authorize",
    token_endpoint: "https://auth.atlassian.com/oauth/token"
  },
  capabilities: ["tools", "resources", "notifications"],
  tools: [
    "create_jira_issue", "search_jira_issues", "create_confluence_page",
    "create_bitbucket_repository", "list_accessible_resources"
  ]
};
```

## 14. Advanced Implementation Patterns

### Resource Discovery Pattern
```typescript
// Pattern for services with multiple accessible resources
async loadAccessibleResources(): Promise<void> {
  try {
    const result = await this.executeTool('list_accessible_resources', {});
    if (result.success && result.data) {
      this.accessibleResources = result.data as ServiceResource[];
      console.log(`Loaded ${this.accessibleResources.length} accessible resources`);
    }
  } catch (error) {
    console.error('Failed to load accessible resources:', error);
  }
}
```

### Multi-Service Tool Coordination
```typescript
// Pattern for tools that span multiple services
async createProjectWorkflow(params: {
  projectName: string;
  description: string;
  includeRepository?: boolean;
  includeDocumentation?: boolean;
  includeIssueTracking?: boolean;
}): Promise<WorkflowResult> {
  const results: WorkflowStep[] = [];

  // Step 1: Create repository (if requested)
  if (params.includeRepository) {
    const repoResult = await this.createRepository({
      name: params.projectName,
      description: params.description
    });
    results.push({ service: 'github', action: 'create_repository', result: repoResult });
  }

  // Step 2: Create documentation space (if requested)
  if (params.includeDocumentation) {
    const docsResult = await this.createDocumentationSpace({
      name: params.projectName,
      description: params.description
    });
    results.push({ service: 'confluence', action: 'create_space', result: docsResult });
  }

  // Step 3: Set up issue tracking (if requested)
  if (params.includeIssueTracking) {
    const issueResult = await this.createProject({
      name: params.projectName,
      description: params.description
    });
    results.push({ service: 'jira', action: 'create_project', result: issueResult });
  }

  return {
    success: results.every(r => r.result.success),
    steps: results,
    projectName: params.projectName
  };
}
```

### Event Correlation Pattern
```typescript
// Pattern for correlating events across services
private correlateEvents(event: ServiceEvent): void {
  // Example: GitHub push triggers Jira issue update
  if (event.service === 'github' && event.type === 'push') {
    this.handleGitHubPushEvent(event.payload);
  }

  // Example: Jira issue completion triggers Confluence documentation update
  if (event.service === 'jira' && event.type === 'issue_completed') {
    this.handleJiraIssueCompletion(event.payload);
  }
}

private async handleGitHubPushEvent(payload: GitHubPushPayload): Promise<void> {
  // Extract issue references from commit messages
  const issueReferences = this.extractIssueReferences(payload.commits);

  // Update related Jira issues
  for (const issueRef of issueReferences) {
    await this.updateJiraIssue({
      issueKey: issueRef.key,
      comment: `Code pushed: ${payload.commits.length} commits to ${payload.repository.name}`
    });
  }
}
```

## 15. Performance Optimization Patterns

### Caching Strategy
```typescript
// Implement intelligent caching for frequently accessed data
class ServiceCache {
  private cache = new Map<string, CacheEntry>();
  private readonly defaultTTL = 300000; // 5 minutes

  async get<T>(key: string, fetcher: () => Promise<T>, ttl?: number): Promise<T> {
    const entry = this.cache.get(key);

    if (entry && Date.now() < entry.expiry) {
      return entry.data as T;
    }

    const data = await fetcher();
    this.cache.set(key, {
      data,
      expiry: Date.now() + (ttl || this.defaultTTL)
    });

    return data;
  }
}
```

### Batch Operations Pattern
```typescript
// Pattern for efficient batch operations
async executeBatchOperations<T>(
  operations: BatchOperation[],
  batchSize: number = 10
): Promise<BatchResult<T>[]> {
  const results: BatchResult<T>[] = [];

  for (let i = 0; i < operations.length; i += batchSize) {
    const batch = operations.slice(i, i + batchSize);
    const batchPromises = batch.map(op => this.executeSingleOperation(op));

    try {
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map((result, index) => ({
        operation: batch[index],
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : undefined,
        error: result.status === 'rejected' ? result.reason : undefined
      })));
    } catch (error) {
      console.error('Batch operation failed:', error);
    }

    // Rate limiting between batches
    if (i + batchSize < operations.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return results;
}
```

## 16. Monitoring and Observability

### Metrics Collection Pattern
```typescript
// Comprehensive metrics collection
interface ServiceMetrics {
  requestCount: number;
  errorCount: number;
  averageResponseTime: number;
  authenticationFailures: number;
  rateLimitHits: number;
  cacheHitRate: number;
  activeConnections: number;
  lastSuccessfulRequest: Date;
  lastError: Date;
}

class MetricsCollector {
  private metrics: ServiceMetrics = {
    requestCount: 0,
    errorCount: 0,
    averageResponseTime: 0,
    authenticationFailures: 0,
    rateLimitHits: 0,
    cacheHitRate: 0,
    activeConnections: 0,
    lastSuccessfulRequest: new Date(),
    lastError: new Date(0)
  };

  recordRequest(responseTime: number, success: boolean): void {
    this.metrics.requestCount++;

    if (success) {
      this.metrics.lastSuccessfulRequest = new Date();
    } else {
      this.metrics.errorCount++;
      this.metrics.lastError = new Date();
    }

    // Update average response time with exponential moving average
    const alpha = 0.1;
    this.metrics.averageResponseTime =
      (alpha * responseTime) + ((1 - alpha) * this.metrics.averageResponseTime);
  }

  getHealthScore(): number {
    const errorRate = this.metrics.errorCount / Math.max(this.metrics.requestCount, 1);
    const responseTimeScore = Math.max(0, 1 - (this.metrics.averageResponseTime / 10000));
    const uptimeScore = Date.now() - this.metrics.lastError.getTime() > 300000 ? 1 : 0.5;

    return (1 - errorRate) * 0.4 + responseTimeScore * 0.3 + uptimeScore * 0.3;
  }
}
```

This comprehensive implementation pattern provides everything needed to create robust, secure, and maintainable MCP service integrations following the proven patterns established in the Alpine Intellect platform.

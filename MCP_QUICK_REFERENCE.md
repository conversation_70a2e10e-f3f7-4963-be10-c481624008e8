# MCP Connection Quick Reference Guide

## Configuration Templates

### HTTP/SSE Service Configuration
```json
{
  "service_name": {
    "transport": "http-sse",
    "enabled": true,
    "endpoints": {
      "http": "https://api.service.com/mcp/v1",
      "sse": "https://api.service.com/mcp/v1/events",
      "discovery": "https://api.service.com/.well-known/oauth-authorization-server"
    },
    "auth": {
      "type": "oauth",
      "client_id": "your_client_id",
      "redirect_uri": "thealpinecode.alpineintellect://auth-callback/service",
      "scopes": ["read", "write"],
      "pkce": true,
      "authorization_endpoint": "https://service.com/oauth/authorize",
      "token_endpoint": "https://service.com/oauth/token"
    },
    "capabilities": ["tools", "resources", "notifications"],
    "tools": [],
    "connection": {
      "timeout": 30000,
      "retry_attempts": 3,
      "retry_delay": 1000
    },
    "cache": {
      "enabled": true,
      "ttl": 300000
    }
  }
}
```

### Stdio Service Configuration
```json
{
  "service_name": {
    "transport": "stdio",
    "enabled": true,
    "stdio": {
      "executable": "node",
      "args": ["service-mcp-server.js"],
      "cwd": "/path/to/mcp-servers",
      "env": {
        "SERVICE_CLIENT_ID": "your_client_id"
      },
      "restart_delay": 2000,
      "max_restarts": 3
    },
    "auth": {
      "type": "oauth",
      "client_id": "your_client_id",
      "redirect_uri": "thealpinecode.alpineintellect://auth-callback/service",
      "scopes": ["read", "write"],
      "pkce": true,
      "authorization_endpoint": "https://service.com/oauth/authorize",
      "token_endpoint": "https://service.com/oauth/token"
    },
    "capabilities": ["tools", "resources", "notifications"],
    "tools": [],
    "connection": {
      "timeout": 30000,
      "retry_attempts": 3,
      "retry_delay": 1000
    },
    "cache": {
      "enabled": true,
      "ttl": 300000
    }
  }
}
```

## Code Templates

### Creating a Remote Service Client
```typescript
import { RemoteService } from '../clients/remote-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';

export class MyRemoteService extends RemoteService {
  constructor(config: ServerConfiguration) {
    super('my-service', 'My Service', config);
  }

  protected async performServiceSpecificInitialization(): Promise<void> {
    // Service-specific setup logic
    console.log('Initializing My Service...');
    
    // Verify service-specific requirements
    await this.verifyServiceRequirements();
    
    // Set up service-specific event handlers
    this.setupServiceEventHandlers();
  }

  private async verifyServiceRequirements(): Promise<void> {
    // Check API version compatibility
    const response = await this.transport.sendRequest({
      jsonrpc: "2.0",
      id: "version_check",
      method: "system/version",
      params: {}
    });
    
    if (response.result.version < "1.0.0") {
      throw new Error('Incompatible API version');
    }
  }

  // Service-specific tool methods
  async getProjects(): Promise<any> {
    return await this.executeTool('get_projects', {});
  }

  async createProject(name: string, description: string): Promise<any> {
    return await this.executeTool('create_project', { name, description });
  }
}
```

### Creating a Local Service Client
```typescript
import { LocalServiceClient, LocalServiceClientConfig } from '../clients/local-service-client.js';
import { ServerConfiguration } from '../config/mcp-config-types.js';

export class MyLocalService extends LocalServiceClient {
  constructor(config: ServerConfiguration) {
    const localConfig: LocalServiceClientConfig = {
      serviceId: 'my-service',
      displayName: 'My Service',
      serverConfig: {
        executable: config.stdio?.executable || 'node',
        args: config.stdio?.args || ['my-service-server.js'],
        cwd: config.stdio?.cwd,
        env: {
          ...config.stdio?.env,
          MY_SERVICE_API_URL: 'https://api.myservice.com',
          MCP_SERVER_TYPE: 'my-service'
        },
        timeout: config.connection.timeout,
        restartDelay: config.stdio?.restart_delay || 2000,
        maxRestarts: config.stdio?.max_restarts || 3,
        enableLogging: true
      },
      oauthConfig: {
        clientId: config.auth.client_id,
        authorizationEndpoint: config.auth.authorization_endpoint || 'https://myservice.com/oauth/authorize',
        tokenEndpoint: config.auth.token_endpoint || 'https://myservice.com/oauth/token',
        redirectUri: config.auth.redirect_uri,
        scopes: config.auth.scopes,
        pkce: config.auth.pkce
      }
    };

    super(localConfig);
  }

  protected async performServiceSpecificInitialization(): Promise<void> {
    console.log('Performing My Service specific initialization...');
    
    // Verify OAuth configuration
    await this.verifyOAuthConfiguration();
    
    // Set up service-specific configuration
    await this.setupServiceConfiguration();
  }

  private async verifyOAuthConfiguration(): Promise<void> {
    if (!this.oauthService) {
      throw new Error('OAuth service not configured');
    }

    const authStatus = await this.oauthService.getAuthStatus();
    console.log('My Service OAuth status:', authStatus);
  }

  protected async discoverAdditionalCapabilities(): Promise<void> {
    if (!this.transport) return;

    try {
      // Discover service-specific resources
      const resourcesResponse = await this.transport.sendRequest({
        jsonrpc: "2.0",
        id: "discover_resources",
        method: "resources/list",
        params: {}
      });

      console.log('Discovered My Service resources:', resourcesResponse.result);
    } catch (error) {
      console.warn('Failed to discover additional capabilities:', error);
    }
  }

  // Service-specific methods
  async getFiles(): Promise<any> {
    return await this.executeTool('get_files', {});
  }

  async uploadFile(filePath: string): Promise<any> {
    return await this.executeTool('upload_file', { file_path: filePath });
  }
}
```

### Custom Transport Implementation
```typescript
import { MCPRequest, MCPResponse, TransportMetrics } from './http-sse-transport.js';

export interface CustomTransportConfig {
  endpoint: string;
  timeout?: number;
  enableLogging?: boolean;
}

export class CustomTransport {
  private config: CustomTransportConfig;
  private initialized: boolean = false;
  private metrics: TransportMetrics;

  constructor(config: CustomTransportConfig) {
    this.config = {
      timeout: 30000,
      enableLogging: true,
      ...config
    };

    this.metrics = {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      connectionUptime: 0
    };
  }

  async initialize(): Promise<void> {
    console.log('Initializing custom transport...');
    
    try {
      // Custom initialization logic
      await this.performCustomSetup();
      
      this.initialized = true;
      console.log('✅ Custom transport initialized');
    } catch (error) {
      console.error('❌ Failed to initialize custom transport:', error);
      throw error;
    }
  }

  async sendRequest(request: MCPRequest): Promise<MCPResponse> {
    if (!this.initialized) {
      throw new Error('Transport not initialized');
    }

    const startTime = Date.now();
    this.metrics.requestCount++;

    try {
      // Custom request handling logic
      const response = await this.performCustomRequest(request);
      
      // Update metrics
      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime);
      
      return response;
    } catch (error) {
      this.metrics.errorCount++;
      throw error;
    }
  }

  private async performCustomSetup(): Promise<void> {
    // Implement custom setup logic
  }

  private async performCustomRequest(request: MCPRequest): Promise<MCPResponse> {
    // Implement custom request logic
    throw new Error('Not implemented');
  }

  private updateMetrics(responseTime: number): void {
    // Update performance metrics
    const alpha = 0.1;
    this.metrics.averageResponseTime = 
      (alpha * responseTime) + ((1 - alpha) * this.metrics.averageResponseTime);
  }

  isHealthy(): boolean {
    return this.initialized;
  }

  getMetrics(): TransportMetrics {
    return { ...this.metrics };
  }

  async close(): Promise<void> {
    console.log('Closing custom transport...');
    this.initialized = false;
  }
}
```

## Common Patterns

### Error Handling
```typescript
// Robust error handling with retry logic
async executeToolWithRetry(toolName: string, params: any, maxRetries: number = 3): Promise<any> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await this.executeTool(toolName, params);
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Exponential backoff
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      console.warn(`Tool execution failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error(`Tool execution failed after ${maxRetries} attempts: ${lastError.message}`);
}
```

### Health Monitoring
```typescript
// Comprehensive health monitoring
async performHealthCheck(): Promise<HealthStatus> {
  const checks: HealthCheck[] = [];
  
  // Transport health
  checks.push({
    name: 'transport',
    healthy: this.transport?.isHealthy() ?? false,
    details: this.transport?.getMetrics()
  });
  
  // Authentication health
  try {
    const authStatus = await this.getAuthStatus();
    checks.push({
      name: 'authentication',
      healthy: authStatus.authenticated,
      details: authStatus
    });
  } catch (error) {
    checks.push({
      name: 'authentication',
      healthy: false,
      details: { error: error.message }
    });
  }
  
  // Service responsiveness
  try {
    await this.executeTool('ping', {});
    checks.push({
      name: 'responsiveness',
      healthy: true,
      details: { latency: 'normal' }
    });
  } catch (error) {
    checks.push({
      name: 'responsiveness',
      healthy: false,
      details: { error: error.message }
    });
  }
  
  const overallHealth = checks.every(check => check.healthy);
  
  return {
    healthy: overallHealth,
    checks,
    timestamp: new Date(),
    serviceId: this.serviceId
  };
}
```

### Configuration Validation
```typescript
// Custom configuration validation
validateServiceConfiguration(config: ServerConfiguration): ValidationResult {
  const errors: string[] = [];
  
  // Transport-specific validation
  if (config.transport === 'stdio') {
    if (!config.stdio?.executable) {
      errors.push('Stdio transport requires executable path');
    }
  } else if (config.transport === 'http-sse') {
    if (!config.endpoints?.http) {
      errors.push('HTTP/SSE transport requires HTTP endpoint');
    }
    if (!config.endpoints?.sse) {
      errors.push('HTTP/SSE transport requires SSE endpoint');
    }
  }
  
  // OAuth validation
  if (config.auth.type === 'oauth') {
    if (!config.auth.client_id) {
      errors.push('OAuth requires client_id');
    }
    if (!config.auth.redirect_uri) {
      errors.push('OAuth requires redirect_uri');
    }
    if (!config.auth.scopes || config.auth.scopes.length === 0) {
      errors.push('OAuth requires at least one scope');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}
```

## Debugging Tips

### Enable Debug Logging
```typescript
// Enable detailed logging for troubleshooting
const config = {
  ...baseConfig,
  stdio: {
    ...baseConfig.stdio,
    env: {
      ...baseConfig.stdio?.env,
      MCP_LOG_LEVEL: 'debug',
      DEBUG: 'mcp:*'
    }
  }
};
```

### Monitor Performance
```typescript
// Performance monitoring
setInterval(() => {
  const metrics = this.transport.getMetrics();
  console.log('Transport metrics:', {
    requestCount: metrics.requestCount,
    errorRate: (metrics.errorCount / metrics.requestCount * 100).toFixed(2) + '%',
    avgResponseTime: metrics.averageResponseTime.toFixed(2) + 'ms',
    uptime: (metrics.connectionUptime / 1000).toFixed(0) + 's'
  });
}, 60000); // Log every minute
```

### Test Connection
```typescript
// Simple connection test
async testConnection(): Promise<boolean> {
  try {
    const response = await this.transport.sendRequest({
      jsonrpc: "2.0",
      id: "test",
      method: "ping",
      params: {}
    });
    return response.result === "pong";
  } catch (error) {
    console.error('Connection test failed:', error);
    return false;
  }
}
```

This quick reference provides the essential patterns and templates needed for implementing MCP connections in the Alpine Intellect Desktop App.
